#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <time.h>
//#include <linux/mtgpio.h>
#include <log/log.h>
#include "meta_gsensor.h"
#include <HfManagerWrapper.h>
#include "HfSensorType.h"

#define GS_PREFIX   "[Meta] GS: "
#define MGSLOGD(fmt, arg...) ALOGD(GS_PREFIX fmt, ##arg)
#define MGSLOGE(fmt, arg...) ALOGE(GS_PREFIX fmt, ##arg)
#define MIN_GS_SUPOORT_DELAY_NS 2500000 //400hz
#define UNIT_SEC_TO_NS 1.0e9
#define UNIT_MS_TO_NS 1000000
#define SAMPLE_RAWDATA_PERIOD_MS 5 * UNIT_MS_TO_NS
#define TIMEOUT_MS 3000
#define TIMEOUT_SEC 3

static void *hf_manager = NULL;
static void *hf_looper = NULL;
static GS_CNF_CB meta_gs_cb = NULL;

void Meta_GSensor_Register(GS_CNF_CB cb)
{
    meta_gs_cb = cb;
}

bool Meta_GSensor_Open(void)
{
    hf_manager = HfManagerCreate();
    hf_looper = HfLooperCreate(HfManagerGetFd(hf_manager), 64);

    if (HfManagerFindSensor(hf_manager, SENSOR_TYPE_ACCELEROMETER) < 0) {
        MGSLOGE("find gyro sensor fail\n");
        goto err_out;
    }
    return true;

err_out:
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return false;
}

static int gs_exec_cali(GS_CMD_CALI* arg, GS_ACK_CALI *cali)
{
    sensors_event_t data[32] = {0};
    int64_t delay_ns = 0;
    int ret = -1;
    struct timespec start_time, cur_time;
    float delta_time = 0.0f;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));

    if (!arg || !cali) {
        MGSLOGE("arg/cali is null, return\n");
        return ret;
    }

    delay_ns = (int64_t)arg->delay * UNIT_MS_TO_NS;
    if (delay_ns < MIN_GS_SUPOORT_DELAY_NS)
        delay_ns = MIN_GS_SUPOORT_DELAY_NS;

    HfManagerRequestFactoryCalibration(hf_manager, SENSOR_TYPE_ACCELEROMETER, true);
    HfManagerEnableFactoryCalibration(hf_manager, SENSOR_TYPE_ACCELEROMETER);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_ACCELEROMETER, delay_ns, 0);
    clock_gettime(CLOCK_REALTIME, &start_time);

    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MGSLOGE("eventlooper error:%d\n", err);
            ret = -1;
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == CALI_ACTION) {
                MGSLOGD("calibration result:%f,%f,%f,%d\n",
                    data[i].acceleration.x, data[i].acceleration.y, data[i].acceleration.z,
                    data[i].acceleration.status);
                //pc side will *9.80665/65536, thus convert to real cali result
                cali->x = (int)(data[i].acceleration.x * 65536 / 9.80665f);
                cali->y = (int)(data[i].acceleration.y * 65536 / 9.80665f);
                cali->z = (int)(data[i].acceleration.z * 65536 / 9.80665f);
                ret = data[i].acceleration.status;
                goto exit;
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerRequestFactoryCalibration(hf_manager, SENSOR_TYPE_ACCELEROMETER, false);
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_ACCELEROMETER);
    return ret;
}

static int gs_exec_read_raw(GS_ACK_READ_RAW *raw)
{
    sensors_event_t data[32] = {0};
    uint8_t ret = -1;
    struct timespec start_time, cur_time;
    float delta_time = 0.0f;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));

    if (!raw) {
        MGSLOGE("raw is null, return\n");
        return ret;
    }

    HfManagerEnableRawData(hf_manager, SENSOR_TYPE_ACCELEROMETER);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_ACCELEROMETER, SAMPLE_RAWDATA_PERIOD_MS, 0);
    clock_gettime(CLOCK_REALTIME, &start_time);
    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MGSLOGE("eventLooper err:%d\n", err);
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == RAW_ACTION) {
                raw->x = (int)data[i].data[0];
                raw->y = (int)data[i].data[1];
                raw->z = (int)data[i].data[2];
                MGSLOGD("raw data:%d, %d,%d\n", raw->x, raw->y, raw->z);
                ret = 0;
                goto exit;
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerDisableRawData(hf_manager, SENSOR_TYPE_ACCELEROMETER);
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_ACCELEROMETER);
    return ret;
}

void Meta_GSensor_OP(GS_REQ *req)
{
    GS_CNF cnf;
    memset(&cnf, 0, sizeof(GS_CNF));
    cnf.header.id = FT_GSENSOR_CNF_ID;
    cnf.header.token = req->header.token;
    cnf.op = req->op;
    switch (req->op) {
        case GS_OP_CALI:
            cnf.gs_err = gs_exec_cali(&req->cmd.cali, &cnf.ack.cali);
            if (cnf.gs_err)
                cnf.status = META_FAILED;
            else
                cnf.status = META_SUCCESS;
        break;

        case GS_OP_READ_RAW:
            cnf.gs_err = gs_exec_read_raw(&cnf.ack.readraw);
            if (cnf.gs_err)
                cnf.status = META_FAILED;
            else
                cnf.status = META_SUCCESS;
        break;

        default:
            cnf.gs_err = -EINVAL;
            cnf.status = META_FAILED;
            MGSLOGE("unknown OP: %d\n", req->op);
        break;
    }

    if (meta_gs_cb)
        meta_gs_cb(&cnf);
    else
        WriteDataToPC(&cnf, sizeof(GS_CNF), NULL, 0);
}

bool Meta_GSensor_Close(void)
{
    MGSLOGE("Meta_GSensor_Close\n");
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return true;
}
