This directory contains libmeta_gpio library


WHAT IT DOES?
=============
It provides gpio api for meta mode test,which to do GPIO test with related pin.

HOW IT WAS BUILT?
==================

and the following libs from MediaTek:
1. libft

All source/dependency modules of this module are already put in
'vendor/mediatek/proprietary/platform/${CHIP_NAME}/external/meta/gpio' folder.


HOW TO USE IT?
==============

Files in this directory is used to
generate a library which's name is 'libmeta_gpio'



The lib 'libmeta_gpio' is loaded when meta_tst and related libs,
which for GPIO operation.

All the source code of this library were written by MediaTek co..

