#include <sys/types.h>
#include <type.h>
#include "CFG_Wifi_File.h"
#include <Custom_NvRam_LID.h>
#include "meta_wifi_ch_group.h"

int test_data_ok = 1234;  /* magic number for checking dlsym ok */
int iFileWIFILID=AP_CFG_RDEB_FILE_WIFI_LID;

#if WIFI_NVRAM_VERSION
int VAR_WIFI_NVRAM_VERSION = WIFI_NVRAM_VERSION;
#else
int VAR_WIFI_NVRAM_VERSION = 0;
#endif

#if META_SUPPORT_PRODUCT_LINE_CAL
int VAR_META_SUPPORT_PRODUCT_LINE_CAL = META_SUPPORT_PRODUCT_LINE_CAL;
#else
int VAR_META_SUPPORT_PRODUCT_LINE_CAL = 0;
#endif

#ifndef OFFSET_OF
#define OFFSET_OF(_type, _field)    offsetof(_type, _field)
#endif /* OFFSET_OF */

#define WIFI_GEN_VER (WIFI_NVRAM_VERSION & 0xF000)
#define CONNAC_SOC7_0                   0x7000
#define CONNAC_SOC3_0                   0x3000
#define CONNAC_SOC2_0                   0x2000
#define SUPPORT_SOC3_0_DNL_VER          0x3030

#if (WIFI_GEN_VER == CONNAC_SOC3_0)
#define CFG_TSSI_CH_GT_SAME 1 // all gt table apply the same value
#else
#define CFG_TSSI_CH_GT_SAME 0 // all gt table apply the same value
#endif

#if (WIFI_NVRAM_VERSION >= SUPPORT_SOC3_0_DNL_VER)
#define CFG_DNL_CAL 1
#else
#define CFG_DNL_CAL 0
#endif

#if (META_SUPPORT_PRODUCT_LINE_CAL == 1)
#if(WIFI_GEN_VER == CONNAC_SOC7_0)
#define DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM 14
#define DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM 32
#define DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM  30

int MAX_NVRAM_ACCESS_SIZE = 256;
int NVRAM_G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_A_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_6G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM;

int NVRAM_G_BAND_TSSI_CH_OFS_SIZE = (sizeof(UINT_8)*DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_A_BAND_TSSI_CH_OFS_SIZE = (sizeof(UINT_8)*DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_6G_BAND_TSSI_CH_OFS_SIZE = (sizeof(UINT_8)*DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/

int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF0path.ucTssiChOfs[0]));
int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF1path.ucTssiChOfs[0]));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF0path.rTssiChOfs[0].ucTssiChOfsLow));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF1path.rTssiChOfs[0].ucTssiChOfsLow));
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r6GWF0path.rTssiChOfs[0].ucTssiChOfsLow));
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r6GWF1path.rTssiChOfs[0].ucTssiChOfsLow));


#elif (WIFI_GEN_VER == CONNAC_SOC3_0)
int VAR_TSSI_CH_OFFSET_TH_GT7 = TSSI_CH_OFFSET_TH_GT7;
int VAR_TSSI_CH_OFFSET_TH_GT6 = TSSI_CH_OFFSET_TH_GT6;
int VAR_TSSI_CH_OFFSET_TH_GT5 = TSSI_CH_OFFSET_TH_GT5;
int VAR_TSSI_CH_OFFSET_TH_GT4 = TSSI_CH_OFFSET_TH_GT4;
int VAR_TSSI_CH_OFFSET_TH_GT3 = TSSI_CH_OFFSET_TH_GT3;
int VAR_TSSI_CH_OFFSET_TH_GT2 = TSSI_CH_OFFSET_TH_GT2;
int VAR_TSSI_CH_OFFSET_TH_GT1 = TSSI_CH_OFFSET_TH_GT1;

#define DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM 14
#define DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM 32
#define DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM  0

int MAX_NVRAM_ACCESS_SIZE = 256;
int NVRAM_G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_A_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_6G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM;

int NVRAM_G_BAND_TSSI_CH_OFS_SIZE = (sizeof(WIFI_NVRAM_TSSI_CH_OFS_T)*DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_A_BAND_TSSI_CH_OFS_SIZE = (sizeof(WIFI_NVRAM_TSSI_CH_OFS_T)*DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/

int NVRAM_6G_BAND_TSSI_CH_OFS_SIZE = 0;

int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF0path.rTssiChOfs[0].ucTssiChOfsGT2));
int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF1path.rTssiChOfs[0].ucTssiChOfsGT2));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF0path.rTssiChOfs[0].ucTssiChOfsGT2));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF1path.rTssiChOfs[0].ucTssiChOfsGT2));
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = 0;
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = 0;


#if (CFG_DNL_CAL == 1)
#define DEF_NVRAM_G_BAND_TSSI_DNL_GROUP_NUM 14
#define DEF_NVRAM_A_BAND_TSSI_DNL_GROUP_NUM 8

int NVRAM_G_BAND_TSSI_DNL_GROUP_NUM = DEF_NVRAM_G_BAND_TSSI_DNL_GROUP_NUM;
int NVRAM_A_BAND_TSSI_DNL_GROUP_NUM = DEF_NVRAM_A_BAND_TSSI_DNL_GROUP_NUM;
int NVRAM_G_BAND_TSSI_DNL_OFS_SIZE = (sizeof(WIFI_NVRAM_TX_DNL_T)*DEF_NVRAM_G_BAND_TSSI_DNL_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_A_BAND_TSSI_DNL_OFS_SIZE = (sizeof(WIFI_NVRAM_TX_DNL_T)*DEF_NVRAM_A_BAND_TSSI_DNL_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF0path.rTxDnl[0].ucTxDnlCckGT0));
int NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF1path.rTxDnl[0].ucTxDnlCckGT0));
int NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF0path.rTxDnl[0].ucTxDnlCckGT0));
int NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF1path.rTxDnl[0].ucTxDnlCckGT0));
#endif
#define DEF_NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM 1
#define DEF_NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM 8

int NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM = DEF_NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM;
int NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM = DEF_NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM;
int NVRAM_G_BAND_LNA_GAIN_CAL_OFS_SIZE = (sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T)*DEF_NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_A_BAND_LNA_GAIN_CAL_OFS_SIZE = (sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T)*DEF_NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF0path.rLnaGainCal[0].ucRxCal1));
int NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF1path.rLnaGainCal[0].ucRxCal1));
int NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF0path.rLnaGainCal[0].ucRxCal1));
int NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF1path.rLnaGainCal[0].ucRxCal1));
#else
#define DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM 14
#define DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM 32
#define DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM  0


int MAX_NVRAM_ACCESS_SIZE = 256;
int NVRAM_G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_A_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_6G_BAND_TSSI_CH_GROUP_NUM = DEF_NVRAM_6G_BAND_TSSI_CH_GROUP_NUM;
int NVRAM_G_BAND_TSSI_CH_OFS_SIZE = (sizeof(UINT_8)*DEF_NVRAM_G_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/
int NVRAM_A_BAND_TSSI_CH_OFS_SIZE = (sizeof(UINT_8)*DEF_NVRAM_A_BAND_TSSI_CH_GROUP_NUM); /*unit: 1 byte*/

int NVRAM_6G_BAND_TSSI_CH_OFS_SIZE = 0;
int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF0path.aucTx2G4TssiChannelOffsetLowCh[0]));
int NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r2G4WF1path.aucTx2G4TssiChannelOffsetLowCh[0]));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF0path.rTxTssiChannelOffset[0].ucTxPowerOffsetLow));
int NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1 = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, r5GWF1path.rTxTssiChannelOffset[0].ucTxPowerOffsetLow));
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0 = 0;
int NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1 = 0;

int NVRAM_TSSI_STEP_OFSET = (OFFSET_OF(WIFI_CFG_PARAM_STRUCT, rSys.u1TssiStep));

#endif /*#if (WIFI_GEN_VER == CONNAC_SOC3_0)*/

/** channel group boundary (common category) */
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_00   1
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_01   2
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_02   3
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_03   4
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_04   5
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_05   6
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_06   7
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_07   8
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_08   9
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_09  10
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_10  11
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_11  12
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_12  13
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_13  14
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_14 255
#define CH_GROUP_COMMON_G_BAND_BOUNDARY_15 255

/** channel group boundary (common category) */
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_00   34
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_01   50
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_02   66
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_03   98
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_04  114
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_05  130
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_06  147
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_07  182
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_08  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_09  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_10  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_11  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_12  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_13  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_14  255
#define CH_GROUP_COMMON_A_BAND_BOUNDARY_15  255

/** channel group boundary (Tssi Ch offset for G-Band) */
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_00    1
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_01    2
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_02    3
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_03    4
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_04    5
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_05    6
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_06    7
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_07    8
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_08    9
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_09   10
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_10   11
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_11   12
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_12   13
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_13   14
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_14  255
#define CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_15  255

/** sub-group boundary (TX for G-Band) */
#define CH_SUB_GROUP_G_BAND_NOT_ORDERED_NUM     0
#define CH_SUB_GROUP_G_BAND_BOUNDARY_00       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_01       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_02       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_03       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_04       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_05       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_06       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_07       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_08       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_09       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_10       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_11       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_12       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_13       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_14       255
#define CH_SUB_GROUP_G_BAND_BOUNDARY_15       255

#if (WIFI_GEN_VER == CONNAC_SOC7_0)
/** channel group boundary (Tssi Ch offset for A-Band) */
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_00  30
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_01  50
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_02  56
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_03  66
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_04  80
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_05  98
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_06  104
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_07  114
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_08  120
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_09  130
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_10  139
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_11  144
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_12  155
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_13  164
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_14  171
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_15  181


/** channel power offset sub-group boundary (TX for A-Band) */
#define CH_SUB_GROUP_A_BAND_NOT_ORDERED_NUM    1
#define CH_SUB_GROUP_A_BAND_BOUNDARY_00       181
#define CH_SUB_GROUP_A_BAND_BOUNDARY_01       40
#define CH_SUB_GROUP_A_BAND_BOUNDARY_02       53
#define CH_SUB_GROUP_A_BAND_BOUNDARY_03       61
#define CH_SUB_GROUP_A_BAND_BOUNDARY_04       75
#define CH_SUB_GROUP_A_BAND_BOUNDARY_05       89
#define CH_SUB_GROUP_A_BAND_BOUNDARY_06       102
#define CH_SUB_GROUP_A_BAND_BOUNDARY_07       110
#define CH_SUB_GROUP_A_BAND_BOUNDARY_08       118
#define CH_SUB_GROUP_A_BAND_BOUNDARY_09       125
#define CH_SUB_GROUP_A_BAND_BOUNDARY_10       134
#define CH_SUB_GROUP_A_BAND_BOUNDARY_11       142
#define CH_SUB_GROUP_A_BAND_BOUNDARY_12       150
#define CH_SUB_GROUP_A_BAND_BOUNDARY_13       160
#define CH_SUB_GROUP_A_BAND_BOUNDARY_14       168
#define CH_SUB_GROUP_A_BAND_BOUNDARY_15       175


/** channel group boundary (Tssi Ch offset for A-Band) */
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_00  15
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_01  30
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_02  48
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_03  64
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_04  80
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_05  94
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_06  110
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_07  126
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_08  142
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_09  158
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_10  174
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_11  190
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_12  206
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_13  222
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_14  239
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_15  255

/** channel power offset sub-group boundary (TX for A-Band) */
#define CH_SUB_GROUP_6G_BAND_NOT_ORDERED_NUM   0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_00       6
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_01       22
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_02       38
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_03       54
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_04       70
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_05       86
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_06       102
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_07       118
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_08       134
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_09       150
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_10       166
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_11       182
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_12       198
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_13       214
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_14       230
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_15       255
#else
/** channel group boundary (Tssi Ch offset for A-Band) */
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_00    5
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_01   34
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_02   40
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_03   50
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_04   56
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_05   66
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_06   80
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_07   98
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_08  104
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_09  114
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_10  120
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_11  130
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_12  136
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_13  144
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_14  157
#define CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_15  182


/** channel power offset sub-group boundary (TX for A-Band) */
#define CH_SUB_GROUP_A_BAND_NOT_ORDERED_NUM    0
#define CH_SUB_GROUP_A_BAND_BOUNDARY_00        190
#define CH_SUB_GROUP_A_BAND_BOUNDARY_01        17
#define CH_SUB_GROUP_A_BAND_BOUNDARY_02        37
#define CH_SUB_GROUP_A_BAND_BOUNDARY_03        45
#define CH_SUB_GROUP_A_BAND_BOUNDARY_04        53
#define CH_SUB_GROUP_A_BAND_BOUNDARY_05        61
#define CH_SUB_GROUP_A_BAND_BOUNDARY_06        75
#define CH_SUB_GROUP_A_BAND_BOUNDARY_07        89
#define CH_SUB_GROUP_A_BAND_BOUNDARY_08       102
#define CH_SUB_GROUP_A_BAND_BOUNDARY_09       110
#define CH_SUB_GROUP_A_BAND_BOUNDARY_10       118
#define CH_SUB_GROUP_A_BAND_BOUNDARY_11       125
#define CH_SUB_GROUP_A_BAND_BOUNDARY_12       134
#define CH_SUB_GROUP_A_BAND_BOUNDARY_13       142
#define CH_SUB_GROUP_A_BAND_BOUNDARY_14       153
#define CH_SUB_GROUP_A_BAND_BOUNDARY_15       163


/** channel group boundary (Tssi Ch offset for A-Band) */
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_00  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_01  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_02  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_03  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_04  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_05  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_06  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_07  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_08  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_09  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_10  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_11  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_12  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_13  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_14  0
#define CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_15  0


/** channel power offset sub-group boundary (TX for A-Band) */
#define CH_SUB_GROUP_6G_BAND_NOT_ORDERED_NUM   0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_00       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_01       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_02       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_03       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_04       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_05       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_06       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_07       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_08       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_09       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_10       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_11       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_12       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_13       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_14       0
#define CH_SUB_GROUP_6G_BAND_BOUNDARY_15       0

#endif /*#if (WIFI_GEN_VER == CONNAC_SOC7_0)*/


CH_GROUP_CLASS var_arChGroupTbl[CH_GROUP_ITEM_NUM][BAND_NUM] =
{
    {   /* 2G channel group (COMMON) */
        {
            CH_GROUP_ITEM_COMMON,
            NVRAM_COMMON_CATEGORY_G_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_COMMON_G_BAND_BOUNDARY_00, CH_GROUP_COMMON_G_BAND_BOUNDARY_01, CH_GROUP_COMMON_G_BAND_BOUNDARY_02, CH_GROUP_COMMON_G_BAND_BOUNDARY_03,
                CH_GROUP_COMMON_G_BAND_BOUNDARY_04, CH_GROUP_COMMON_G_BAND_BOUNDARY_05, CH_GROUP_COMMON_G_BAND_BOUNDARY_06, CH_GROUP_COMMON_G_BAND_BOUNDARY_07,
                CH_GROUP_COMMON_G_BAND_BOUNDARY_08, CH_GROUP_COMMON_G_BAND_BOUNDARY_09, CH_GROUP_COMMON_G_BAND_BOUNDARY_10, CH_GROUP_COMMON_G_BAND_BOUNDARY_11,
                CH_GROUP_COMMON_G_BAND_BOUNDARY_12, CH_GROUP_COMMON_G_BAND_BOUNDARY_13, CH_GROUP_COMMON_G_BAND_BOUNDARY_14, CH_GROUP_COMMON_G_BAND_BOUNDARY_15
            }
        },
        /* 5G channel group (COMMON) */
        {
            CH_GROUP_ITEM_COMMON,
            NVRAM_COMMON_CATEGORY_A_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_COMMON_A_BAND_BOUNDARY_00, CH_GROUP_COMMON_A_BAND_BOUNDARY_01, CH_GROUP_COMMON_A_BAND_BOUNDARY_02, CH_GROUP_COMMON_A_BAND_BOUNDARY_03,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_04, CH_GROUP_COMMON_A_BAND_BOUNDARY_05, CH_GROUP_COMMON_A_BAND_BOUNDARY_06, CH_GROUP_COMMON_A_BAND_BOUNDARY_07,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_08, CH_GROUP_COMMON_A_BAND_BOUNDARY_09, CH_GROUP_COMMON_A_BAND_BOUNDARY_10, CH_GROUP_COMMON_A_BAND_BOUNDARY_11,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_12, CH_GROUP_COMMON_A_BAND_BOUNDARY_13, CH_GROUP_COMMON_A_BAND_BOUNDARY_14, CH_GROUP_COMMON_A_BAND_BOUNDARY_15
            }
        },
        /* 6G channel group (COMMON) */
        {
            CH_GROUP_ITEM_COMMON,
            NVRAM_COMMON_CATEGORY_A_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_COMMON_A_BAND_BOUNDARY_00, CH_GROUP_COMMON_A_BAND_BOUNDARY_01, CH_GROUP_COMMON_A_BAND_BOUNDARY_02, CH_GROUP_COMMON_A_BAND_BOUNDARY_03,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_04, CH_GROUP_COMMON_A_BAND_BOUNDARY_05, CH_GROUP_COMMON_A_BAND_BOUNDARY_06, CH_GROUP_COMMON_A_BAND_BOUNDARY_07,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_08, CH_GROUP_COMMON_A_BAND_BOUNDARY_09, CH_GROUP_COMMON_A_BAND_BOUNDARY_10, CH_GROUP_COMMON_A_BAND_BOUNDARY_11,
                CH_GROUP_COMMON_A_BAND_BOUNDARY_12, CH_GROUP_COMMON_A_BAND_BOUNDARY_13, CH_GROUP_COMMON_A_BAND_BOUNDARY_14, CH_GROUP_COMMON_A_BAND_BOUNDARY_15
            }
        }
    },
    {   /* 2G channel group (TSSI_CH) */
        {
            CH_GROUP_ITEM_TSSI_CH,
            NVRAM_TSSI_CH_OFFSET_G_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_00, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_01, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_02, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_03,
                CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_04, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_05, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_06, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_07,
                CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_08, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_09, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_10, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_11,
                CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_12, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_13, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_14, CH_GROUP_TSSI_CH_OFS_G_BAND_BOUNDARY_15
            }
        },
        /* 5G channel group (TSSI_CH) */
        {
            CH_GROUP_ITEM_TSSI_CH,
            NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_00, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_01, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_02, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_03,
                CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_04, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_05, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_06, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_07,
                CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_08, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_09, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_10, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_11,
                CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_12, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_13, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_14, CH_GROUP_TSSI_CH_OFS_A_BAND_BOUNDARY_15
            }
        },
        /* 6G channel group (TSSI_CH) */
        {
            CH_GROUP_ITEM_TSSI_CH,
            NVRAM_TSSI_CH_OFFSET_6G_BAND_CH_GROUP_NUM,
            {
                CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_00, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_01, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_02, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_03,
                CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_04, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_05, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_06, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_07,
                CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_08, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_09, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_10, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_11,
                CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_12, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_13, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_14, CH_GROUP_TSSI_CH_OFS_6G_BAND_BOUNDARY_15
            }
        }
    },
};

CH_SUB_GROUP_CLASS var_arSubGroupTbl[CH_SUB_GROUP_ITEM_NUM][BAND_NUM] =
{
    {   /* 2G channel group (TX) */
        {
            CH_SUB_GROUP_TSSI_CH,
            CH_SUB_GROUP_2G4_ITEM_NUM, CH_SUB_GROUP_G_BAND_NOT_ORDERED_NUM,
            {
                CH_SUB_GROUP_G_BAND_BOUNDARY_00, CH_SUB_GROUP_G_BAND_BOUNDARY_01, CH_SUB_GROUP_G_BAND_BOUNDARY_02, CH_SUB_GROUP_G_BAND_BOUNDARY_03,
                CH_SUB_GROUP_G_BAND_BOUNDARY_04, CH_SUB_GROUP_G_BAND_BOUNDARY_05, CH_SUB_GROUP_G_BAND_BOUNDARY_06, CH_SUB_GROUP_G_BAND_BOUNDARY_07,
                CH_SUB_GROUP_G_BAND_BOUNDARY_08, CH_SUB_GROUP_G_BAND_BOUNDARY_09, CH_SUB_GROUP_G_BAND_BOUNDARY_10, CH_SUB_GROUP_G_BAND_BOUNDARY_11,
                CH_SUB_GROUP_G_BAND_BOUNDARY_12, CH_SUB_GROUP_G_BAND_BOUNDARY_13, CH_SUB_GROUP_G_BAND_BOUNDARY_14, CH_SUB_GROUP_G_BAND_BOUNDARY_15
            }
        },
        /* 5G channel group (TX) */
        {
            CH_SUB_GROUP_TSSI_CH,
            CH_SUB_GROUP_5G_ITEM_NUM, CH_SUB_GROUP_A_BAND_NOT_ORDERED_NUM,
            {
                CH_SUB_GROUP_A_BAND_BOUNDARY_00, CH_SUB_GROUP_A_BAND_BOUNDARY_01, CH_SUB_GROUP_A_BAND_BOUNDARY_02, CH_SUB_GROUP_A_BAND_BOUNDARY_03,
                CH_SUB_GROUP_A_BAND_BOUNDARY_04, CH_SUB_GROUP_A_BAND_BOUNDARY_05, CH_SUB_GROUP_A_BAND_BOUNDARY_06, CH_SUB_GROUP_A_BAND_BOUNDARY_07,
                CH_SUB_GROUP_A_BAND_BOUNDARY_08, CH_SUB_GROUP_A_BAND_BOUNDARY_09, CH_SUB_GROUP_A_BAND_BOUNDARY_10, CH_SUB_GROUP_A_BAND_BOUNDARY_11,
                CH_SUB_GROUP_A_BAND_BOUNDARY_12, CH_SUB_GROUP_A_BAND_BOUNDARY_13, CH_SUB_GROUP_A_BAND_BOUNDARY_14, CH_SUB_GROUP_A_BAND_BOUNDARY_15
            }
        },
        /* 6G channel group (TX) */
        {
            CH_SUB_GROUP_TSSI_CH,
            CH_SUB_GROUP_5G_ITEM_NUM, CH_SUB_GROUP_6G_BAND_NOT_ORDERED_NUM,
            {
                CH_SUB_GROUP_6G_BAND_BOUNDARY_00, CH_SUB_GROUP_6G_BAND_BOUNDARY_01, CH_SUB_GROUP_6G_BAND_BOUNDARY_02, CH_SUB_GROUP_6G_BAND_BOUNDARY_03,
                CH_SUB_GROUP_6G_BAND_BOUNDARY_04, CH_SUB_GROUP_6G_BAND_BOUNDARY_05, CH_SUB_GROUP_6G_BAND_BOUNDARY_06, CH_SUB_GROUP_6G_BAND_BOUNDARY_07,
                CH_SUB_GROUP_6G_BAND_BOUNDARY_08, CH_SUB_GROUP_6G_BAND_BOUNDARY_09, CH_SUB_GROUP_6G_BAND_BOUNDARY_10, CH_SUB_GROUP_6G_BAND_BOUNDARY_11,
                CH_SUB_GROUP_6G_BAND_BOUNDARY_12, CH_SUB_GROUP_6G_BAND_BOUNDARY_13, CH_SUB_GROUP_6G_BAND_BOUNDARY_14, CH_SUB_GROUP_6G_BAND_BOUNDARY_15
            }
        }
    }
};
#endif /*#if (META_SUPPORT_PRODUCT_LINE_CAL == 1)*/