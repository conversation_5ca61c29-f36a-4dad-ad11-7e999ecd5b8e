#ifndef TRUSTKERNEL_EXTERNAL_COMMAND_H_
#define TRUSTKERNEL_EXTERNAL_COMMAND_H_

#define KPHA_META_PREFIX        (0xC0U)
#define KPHA_META_CMD_MASK      ((1U << 6) - 1)
#define KPHA_META_CMD(cmdid)    ((unsigned char) (KPHA_META_PREFIX | ((cmdid) & KPHA_META_CMD_MASK)))

#define KPHA_RECV_FILE          KPHA_META_CMD(0)

#define KPHA_IMPORT_CFG_FILE    KPHA_META_CMD(1)
#define KPHA_IMPORT_TA_DATA     KPHA_META_CMD(2)
#define KPHA_IMPORT_TA_KEY      KPHA_META_CMD(3)

#define KPHA_GEN_KEY            KPHA_META_CMD(4)

#define KPHA_EXPORT_KEY         KPHA_META_CMD(5)
#define KP<PERSON>_EXPORT_DEV_INF     KPHA_META_CMD(6)

#define K<PERSON><PERSON>_GET_SN_CHIPID      KPHA_META_CMD(7)
#define KPHA_SEND_FILE          KPHA_META_CMD(8)
/* newly added since version 3.0.0 */

#define KPHA_WAIT_FOR_DEVICE    KPHA_META_CMD(11)
#define KPHA_GET_VERSION        KPHA_META_CMD(12)
#define KPHA_PROGRAM_CERT       KPHA_META_CMD(13)
#define KPHA_GET_PL_STATUS      KPHA_META_CMD(14)

/* cmd15 is used by clear_keystate */
#define KPHA_GENERIC_EXECUTE_CMD    KPHA_META_CMD(16)
#define KPHA_GET_TRUSTSTORE     KPHA_META_CMD(17)
#define KPHA_REVOKE_CERT        KPHA_META_CMD(18)

/* end for version 3.x */

#define KPHA_SHUTDOWN_DEV       KPHA_META_CMD(19)
#define KPHA_READ_CMD_RESPONSE  KPHA_META_CMD(20)

#define KPHA_IMPORT_TA_DATA_BY_UUID KPHA_META_CMD(21)
/* keybox */
#define KPHA_INSTALL_KEYBOX     KPHA_META_CMD(30)

#define KPHA_DACCESS_RESET_DEVICE       KPHA_META_CMD(40)
#define KPHA_DACCESS_PRE_ENROLL_DEVICE  KPHA_META_CMD(41)
#define KPHA_DACCESS_ENROLL_DEVICE      KPHA_META_CMD(42)
#define KPHA_DACCESS_GET_LOCK_STATUS    KPHA_META_CMD(43)
#define KPHA_DACCESS_GET_DEVICE_UUID    KPHA_META_CMD(44)
#define KPHA_DACCESS_GET_DEVICE_GROUP   KPHA_META_CMD(45)
int handle_trustkernel_meta_command(unsigned char cmd, char **pbuf, int *psize, unsigned char *r);

#endif

