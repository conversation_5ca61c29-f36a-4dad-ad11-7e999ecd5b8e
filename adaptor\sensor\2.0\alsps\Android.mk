LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)
LOCAL_STATIC_LIBRARIES := libft
LOCAL_SHARED_LIBRARIES := liblog libhardware
LOCAL_SRC_FILES := meta_alsps.c
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)
LOCAL_HEADER_LIBRARIES += libsensor_core
LOCAL_SHARED_LIBRARIES += libhfmanagerwrapper
LOCAL_MODULE := libmeta_alsps
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
include $(MTK_STATIC_LIBRARY)
