/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein is
 * confidential and proprietary to MediaTek Inc. and/or its licensors. Without
 * the prior written permission of MediaTek inc. and/or its licensors, any
 * reproduction, modification, use or disclosure of MediaTek Software, and
 * information contained herein, in whole or in part, shall be strictly
 * prohibited.
 *
 * MediaTek Inc. (C) 2020. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER
 * ON AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR
 * NONINFRINGEMENT. NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH
 * RESPECT TO THE SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY,
 * INCORPORATED IN, OR SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES
 * TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * RECEIVER EXPRESSLY ACKNOWLEDGES THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO
 * OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES CONTAINED IN MEDIATEK
 * SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE
 * RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S
 * ENTIRE AND CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE
 * RELEASED HEREUNDER WILL BE, AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE
 * MEDIATEK SOFTWARE AT ISSUE, OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE
 * CHARGE PAID BY RECEIVER TO MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek
 * Software") have been modified by MediaTek Inc. All revisions are subject to
 * any receiver's applicable license agreements with MediaTek Inc.
 */

#include <math.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include "../../meta_afc.h"
#include "../../../meta_utils/meta_cal_utils.h"
#include "lp_afc.h"

#define MT6983_TIA2_GPS_TSX_DATA_ADDR		0x1C015ED0
#define MT6983_TIA2_GPS_TSX_DATA_READY_MASK	1
#define MT6983_TIA2_GPS_TSX_DATA_READY_SHIFT	15
#define MT6983_TIA2_GPS_TSX_DATA_MASK		0xFFFF

//PLT_CORE_AFC plt_core_afc = mt6983_core_afc;

static unsigned int get_rxv(unsigned char cycle)
{
    int retry = 30;
    unsigned int val = 0;
    char cmd[MAX_CMD_SIZE];
    memset(cmd, 0, sizeof(cmd));

    // RXV for band 0: 0x8308
    if (cycle == 0) { // RXV0
        strcpy(cmd, "get_mcr 0x83081154");
    } else if (cycle == 38) { // RXV38
        strcpy(cmd, "get_mcr 0x83081220");
    } else if (cycle == 39) { // RXV39
        strcpy(cmd, "get_mcr 0x83081224");
    } else {
        return 0;
    }

    while (--retry >= 0) {
        val = get_wifi_reg_val(cmd);

        if (val == 0) {
            continue;
        }

        break;
    }

    return val;
}

static int get_tx_mode_and_cbw(unsigned int *tx_mode, unsigned int *cbw)
{
    int ret = 0;
    unsigned int rxv0 = 0;

    // 0X1150   RO_BAND_PHYCTRL_RXV0   31   0   RO_BAND_RXV_C_B_0_LO
    rxv0 = get_rxv(0);

    if (rxv0 == 0) {
        ERR("Cannot get valid rxv0 for tx mode\n");
        return -ERXV;
    }

    *tx_mode = (rxv0 >> 4) & 0xF; // C_B_0[7:4] Packet format: CCK or OFDM
    *cbw = (rxv0 >> 8) & 0x7; // C_B_0[10:8] cbw

    return ret;
}

float mt6983_query_temperature()
{
    int ret = 0;
    int retry = 10;
    float temp = 0;
    unsigned int orig = 0;
    unsigned int auxadc = 0;
    unsigned char ready_bit = 0;

    while (--retry >= 0) {
        ret = read_tia_reg(MT6983_TIA2_GPS_TSX_DATA_ADDR, &orig);
        ready_bit = (orig >> MT6983_TIA2_GPS_TSX_DATA_READY_SHIFT) & MT6983_TIA2_GPS_TSX_DATA_READY_MASK;

        if (ready_bit != 1) {
            continue;
        }

        break;
    }

    if (ready_bit == 0) {
        DBG("Retry fail: cannot get valid auxadc\n");
        return -EPMIC_R;
    }

    auxadc = orig & MT6983_TIA2_GPS_TSX_DATA_MASK;
    DBG("Auxadc[%d]\n", auxadc);

    temp = convert_auxadc_to_temperature(auxadc);
    DBG("Temperature[%f]\n", temp);

    return temp;
}

int mt6983_get_foe(double *avg_foe)
{
    const unsigned char VALID_CNT_MAX = 3;
    int ret = 0;
    int rxv_foe = 0;
    unsigned char sign_bit = 0;
    unsigned int rxvl = 0;
    unsigned int rxvh = 0;
    unsigned int foe_const = 0;
    unsigned int cbw = 0;
    unsigned int tx_mode = 0;
    double accum_foe = 0;

    // Need to avoid zero foe but also minimize total calibration time
    // We get average foe from three valid rxv_foe
    for (int count = 0; count < VALID_CNT_MAX; count++) {
        ret = get_tx_mode_and_cbw(&tx_mode, &cbw);

        if (ret < 0) {
            ERR("Cannot get valid tx mode and cbw\n");
            return -ETX_MODE;
        }

        // OFDM FOE (P_A_0_A, tft_foe[20*n+:20], bits [38:19])
        // 0x1200   RO_BAND_PHYCTRL_RXV38   31   0   RO_BAND_RXV_P_A_0_A_LO
        // 0x1204   RO_BAND_PHYCTRL_RXV39   31   0   RO_BAND_RXV_P_A_0_A_HI
        rxvl = get_rxv(38);

        if (rxvl == 0) {
            ERR("Cannot get valid rxv_l for foe\n");
            return -ERXV;
        }

        rxvh = get_rxv(39);

        if (rxvh == 0) {
            ERR("Cannot get valid rxv_h for foe\n");
            return -ERXV;
        }

        foe_const = ((1 << (cbw + 1)) & 0xf) * 10000000;

        // Calculate rxv_foe according to tx mode
        if (tx_mode == 0) { // CCK
            // CCK only supports cbw = 20
            // CCK FOE (C_B_2, rxv_cck_pd_foe[10:0], bits [37:27])
            // rxv low[31:27] + rxv high[32:37]
            rxv_foe = ((rxvh & 0x3F) << 5) | (rxvl >> 27);
            sign_bit = ((rxv_foe & BIT(10)) >> 10);
            DBG("CCK rxv_foe[0x%08x]\n", rxv_foe);

            if (sign_bit) {
                rxv_foe -= 2048;
            }

            // FOE value = ((FOE)/2^11)*20*1e6
            foe_const = 20000000 >> 11;
            rxv_foe *= foe_const;
        } else { // OFDM
            // OFDM FOE (P_A_0_A, tft_foe[20*n+:20], bits [38:19])
            // rxv low[31:19] + rxv high[32:38]
            rxv_foe = ((rxvh & 0x7F) << 13) | (rxvl >> 19);
            sign_bit = ((rxv_foe & BIT(19)) >> 19);
            DBG("OFDM rxv_foe[0x%08x]\n", rxv_foe);

            if (sign_bit) {
                rxv_foe -= 1048576;
            }

            // FOE value = ((FOE)/2^24)*20*1e6
            foe_const >>= 20;
            rxv_foe = (rxv_foe * foe_const) >> 4;

            if (sign_bit) {
                rxv_foe |= BITS(20, 31);
            }
        }

        // RXV regards RX side as its baseline
        // Change baseline to TX side, i.e. consider instrument as baseline
        rxv_foe *= -1;

        accum_foe += (double)rxv_foe;
        DBG("tx_mode[%d], cbw[%d], rxvl[0x%08x], rxvh[0x%08x], rxv_foe[0x%08x], foe[#%d]=[%d]\n", tx_mode, cbw, rxvl, rxvh, rxv_foe, count, rxv_foe);
    }

    // Get average foe
    *avg_foe = accum_foe / (double)VALID_CNT_MAX;
    DBG("Get avg foe[%lf]\n", *avg_foe);

    return ret;
}

int mt6983_core_afc(float *temperature)
{
    int ret = 0;
    int best_cap_id = 0;
    double cap_id_arr[10] = {0.0, 29.0, 58.0, 87.0, 116.0, 145.0, 174.0, 203.0, 232.0, 255.0};
    unsigned int cap_id_num = 10;

    clock_t t = clock();

    // Get foe based on different cap id
    double *foe_arr = malloc(cap_id_num * sizeof(double));

    if (foe_arr == NULL) {
        ERR("Frequency error table is null\n");
        return -EMALLOC;
    }

    ret = preaction_capid_cal();

    if (ret != 0) {
        free(foe_arr);
        return -EPMIC_W;
    }

    // wait 5ms
    usleep(5000);

    // Start to k best cap ID
    for (unsigned int i = 0; i < cap_id_num; i++) {
        // Set cap id
        ret = set_capid_cal(cap_id_arr[i]);

		// wait 5ms
		usleep(5000);

        if (ret != 0) {
            free(foe_arr);
            return -EPMIC_W;
        }

        // Get foe
        ret = mt6983_get_foe(&foe_arr[i]);

        if (ret != 0) {
            free(foe_arr);
            return ret;
        }
    }

    best_cap_id = evaluate_cap_id(cap_id_num, cap_id_arr, foe_arr);
    free(foe_arr);

    if (best_cap_id >= 0) {
        ret = set_capid_cal(best_cap_id);

        if (ret != 0) {
            return -EPMIC_W;
        }

        DBG("Set best cap id[%d] into PMIC reg successfully\n", best_cap_id);
    }

    t = clock() - t;
    double time_taken = ((double)t) / CLOCKS_PER_SEC; // calculate the elapsed time
    DBG("AFC calibration takes %lf seconds\n", time_taken);

	*temperature = mt6983_query_temperature();
	DBG("AFC calibration get temperature [%f]\n", *temperature);

    return best_cap_id;

}
