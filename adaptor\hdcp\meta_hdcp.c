/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2008
*
*  BY OPENING THIS FILE, BUYER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
*  THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
*  RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO BUYER ON
*  AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
*  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
*  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
*  NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
*  SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
*  SUPPLIED WITH THE MEDIATEK SOFTWARE, AND BUYER AGREES TO LOOK ONLY TO SUCH
*  THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. MEDIATEK SHALL ALSO
*  NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE RELEASES MADE TO BUYER'S
*  SPECIFICATION OR TO CONFORM TO A PARTICULAR STANDARD OR OPEN FORUM.
*
*  BUYER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND CUMULATIVE
*  LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
*  AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
*  OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY BUYER TO
*  MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
*
*  THE TRANSACTION CONTEMPLATED HEREUNDER SHALL BE CONSTRUED IN ACCORDANCE
*  WITH THE LAWS OF THE STATE OF CALIFORNIA, USA, EXCLUDING ITS CONFLICT OF
*  LAWS PRINCIPLES.  ANY DISPUTES, CONTROVERSIES OR CLAIMS ARISING THEREOF AND
*  RELATED THERETO SHALL BE SETTLED BY ARBITRATION IN SAN FRANCISCO, CA, UNDER
*  THE RULES OF THE INTERNATIONAL CHAMBER OF COMMERCE (ICC).
*
*****************************************************************************/

#include <stdio.h>   /* Standard input/output definitions */
#include <string.h>  /* String function definitions */
#include <android/log.h>

#include "meta_hdcp_para.h"


#include "ARM_Hdcp_Errors.h"
#include "ARM_Hdcp_Engine.h"
#include "ARM_Hdcp_Provisioning.h"

//#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <private/android_filesystem_config.h>

#undef   LOG_TAG

#define  LOG_TAG  "HDCP_META"
#define  HDCPLOGI(...)  __android_log_print(ANDROID_LOG_INFO,LOG_TAG,__VA_ARGS__)


static HDCP_CNF hdcp_cnf;

int META_HDCP_init(void)
{
    HDCPLOGI("META_HDCP_init()");
    /* Do nothing */
    return 1;
}


void META_HDCP_deinit(void)
{
    HDCPLOGI("META_HDCP_deinit()");
	/* Do nothing */
}


void changeKeyFileOwner(void)
{
    const char* path1 = "/mnt/vendor/persist/data";
    const char* path2 = "/mnt/vendor/persist/data/dxhdcp2";
    const char* path3 = "/mnt/vendor/persist/data/dxhdcp2/dxhdcp2";
    const char* dataFile = "/mnt/vendor/persist/data/dxhdcp2/dxhdcp2/dxhdcp2.sfs";
    int ret = chown(dataFile, AID_MEDIA, AID_MEDIA);
    if (ret != 0){
        HDCPLOGI("chown() dataFile fail. error:[%d] %s", errno, strerror(errno));
    }

    ret = chown(path1, AID_MEDIA, AID_MEDIA);
    if (ret != 0){
        HDCPLOGI("chown() path1 fail. error:[%d] %s", errno, strerror(errno));
    }

    ret = chown(path2, AID_MEDIA, AID_MEDIA);
    if (ret != 0){
        HDCPLOGI("chown() path2 fail. error:[%d] %s", errno, strerror(errno));
    }

    ret = chown(path3, AID_MEDIA, AID_MEDIA);
    if (ret != 0){
        HDCPLOGI("chown() path3 fail. error:[%d] %s", errno, strerror(errno));
    }
}

bool startKeyProvisioning(unsigned char *data, int dataLen, unsigned char *cekData, int cekLen)
{
    if (dataLen != 572 || cekLen != 16) {
        HDCPLOGI("keyProvisioning() len is wrong! dataLen:%d, cekLen:%d", dataLen, cekLen);
        return false;
    }
    const char* cfgFile = "/vendor/etc/ArmHDCP_MediatekGP.cfg";

    uint32_t result = 0;
    result = ARM_HDCP_Engine_Init(cfgFile);
    if (result != 0)
    {
        HDCPLOGI("ARM_HDCP_Engine_Init failed");
        goto end;
    }
    HDCPLOGI("ARM_HDCP_Engine_Init succeed");

    result = ARM_HDCP_Provisioning_ProvisionWithCEK(data, dataLen, cekData);
    if (result != 0)
    {
        HDCPLOGI("ARM_HDCP_Provisioning_ProvisionWithCEK failed");
        goto end;
    }
    HDCPLOGI("ARM_HDCP_Provisioning_ProvisionWithCEK succeed");

    result = ARM_HDCP_Provisioning_Validate(data, dataLen, cekData);
    if (result != 0)
    {
        HDCPLOGI("ARM_HDCP_Provisioning_Validate failed");
        goto end;
    }
    HDCPLOGI("ARM_HDCP_Provisioning_Validate succeed");


end:
    ARM_HDCP_Engine_Finalize();

    HDCPLOGI("provisioning test finished with return code: 0x%08x\n",(unsigned int)result);

    /* The file permission is 660 by default.
     * So change file owner and group as media for mediaserver process to R/W
     */
    changeKeyFileOwner();

    return (result == 0) ? true : false;
}


void META_HDCP_OP(HDCP_REQ *req, char *peer_buff, unsigned short peer_len)
{
    bool ret;

	memset(&hdcp_cnf, 0, sizeof(HDCP_CNF));
	hdcp_cnf.header.id = FT_HDCP_CNF_ID;
	hdcp_cnf.header.token = req->header.token;
	hdcp_cnf.op = req->op;

    HDCPLOGI("META_HDCP_OP(), op:%d", req->op);

    switch(req->op){
        case HDCP_OP_INSTALL:

            ret = startKeyProvisioning(
                req->cmd.hdcp_install_req.data,
                req->cmd.hdcp_install_req.data_len,
                req->cmd.hdcp_install_req.cek_data,
                req->cmd.hdcp_install_req.cek_len);

            // Operation request is accepted successfully
            hdcp_cnf.status = META_SUCCESS;

            // Operation result
            if (ret == true) {
                hdcp_cnf.result.hdcp_install_cnf.install_result = HDCP_CNF_OK;
            } else {
                hdcp_cnf.result.hdcp_install_cnf.install_result = HDCP_CNF_FAIL;
            }

            WriteDataToPC(&hdcp_cnf, sizeof(HDCP_CNF), NULL, 0);
			break;


	    default:
			HDCPLOGI("Unsupported OP");
			hdcp_cnf.status = META_FAILED;
			WriteDataToPC(&hdcp_cnf, sizeof(HDCP_CNF), NULL, 0);
			break;
	}
}

