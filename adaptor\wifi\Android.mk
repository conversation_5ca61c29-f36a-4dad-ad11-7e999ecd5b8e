LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)

LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)
LOCAL_C_INCLUDES := $(MTK_PATH_SOURCE)/hardware/meta/common/inc \
    $(MTK_PATH_SOURCE)/external/nvram/libnvram

LOCAL_SRC_FILES := meta_wifi.c iwlibstub.c  

$(info "TARGET_BOARD_PLATFORM is $(TARGET_BOARD_PLATFORM)")
$(info "MTK_PATH_SOURCE is $(MTK_PATH_SOURCE)")

ifdef MTK_GENERIC_HAL
$(info "Layer decoupling 2.0")
ifeq ($(MTK_TB_WIFI_3G_MODE), WIFI_ONLY)
LOCAL_CFLAGS += -DSUPPORT_DECOUPLING_2_0
LOCAL_CFLAGS += -DSUPPORT_AFC_C0C1
LOCAL_SRC_FILES += meta_afc/meta_afc.c
LOCAL_SRC_FILES += meta_c0c1/meta_c0c1.c
LOCAL_SRC_FILES += meta_utils/meta_cal_utils.c
LOCAL_SRC_FILES += meta_afc/platform/LD2_lp/lp_afc.c
LOCAL_SRC_FILES += meta_c0c1/platform/LD2_lp/lp_c0c1.c
LOCAL_SRC_FILES += meta_afc/platform/LD2_mt/mt_afc.c
LOCAL_SRC_FILES += meta_c0c1/platform/LD2_mt/mt_c0c1.c
#add other decoupling 2.0 platform
endif

else
$(info "Layer decoupling 1.0")
ifeq ($(MTK_TB_WIFI_3G_MODE), WIFI_ONLY)
ifneq ($(filter mt6885 mt6765 mt6771 mt6761 mt6768 mt6785 mt6877 mt6833 mt6893 mt8168, $(TARGET_BOARD_PLATFORM)),)
ifneq ($(wildcard $(LOCAL_PATH)/meta_afc/platform/$(TARGET_BOARD_PLATFORM)),)
LOCAL_CFLAGS += -DSUPPORT_AFC_C0C1
LOCAL_SRC_FILES += meta_afc/meta_afc.c
LOCAL_SRC_FILES += meta_c0c1/meta_c0c1.c
LOCAL_SRC_FILES += meta_utils/meta_cal_utils.c
LOCAL_SRC_FILES += meta_afc/platform/$(TARGET_BOARD_PLATFORM)/$(TARGET_BOARD_PLATFORM)_afc.c
LOCAL_SRC_FILES += meta_c0c1/platform/$(TARGET_BOARD_PLATFORM)/$(TARGET_BOARD_PLATFORM)_c0c1.c
$(info "Included WiFi only Co-TMS files")
endif
endif
ifneq ($(filter mt6765, $(TARGET_BOARD_PLATFORM)),)
LOCAL_CFLAGS += -DSUPPORT_PMIC_DEV_6765
endif
ifneq ($(filter mt6877, $(TARGET_BOARD_PLATFORM)),)
LOCAL_CFLAGS += -DSUPPORT_DCXO_6877
LOCAL_CFLAGS += -DSUPPORT_PMIC_DEV_6877
endif
LOCAL_C_INCLUDES += $(MTK_PATH_SOURCE)/custom/$(MTK_PLATFORM)/cgen/cfgfileinc
endif

endif

LOCAL_MODULE := libmeta_wifi
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
LOCAL_SHARED_LIBRARIES := libcutils libnetutils libc liblog
LOCAL_STATIC_LIBRARIES := libft
include $(MTK_STATIC_LIBRARY)

###############################################################################
# NVRAM data
###############################################################################
ifneq ($(MTK_TARGET_PROJECT)$(VEXT_TARGET_PROJECT),)
include $(CLEAR_VARS)
LOCAL_SRC_FILES := meta_wifi_data.c
#LOCAL_C_INCLUDES := $(MTK_PATH_SOURCE)/custom/$(MTK_TARGET_PROJECT)/cgen/cfgfileinc
LOCAL_C_INCLUDES := $(MTK_PATH_CUSTOM)/cgen/cfgdefault \
                    $(MTK_PATH_CUSTOM)/cgen/cfgfileinc \
                    $(MTK_PATH_CUSTOM)/cgen/inc \
                    $(MTK_PATH_CUSTOM)/cgen \
            $(MTK_PATH_COMMON)/cgen/cfgdefault \
            $(MTK_PATH_COMMON)/cgen/cfgfileinc \
            $(MTK_PATH_COMMON)/cgen/inc \
            $(MTK_PATH_COMMON)/cgen

LOCAL_MODULE := meta_wifi_data
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
LOCAL_SHARED_LIBRARIES := libc
LOCAL_SDK_VERSION := current
LOCAL_VENDOR_MODULE := true
LOCAL_NDK_STL_VARIANT := c++_static
include $(BUILD_SHARED_LIBRARY)
endif
###############################################################################
# SELF TEST
###############################################################################
BUILD_SELF_TEST := false
ifeq ($(BUILD_SELF_TEST), true)
include $(CLEAR_VARS)
LOCAL_SRC_FILES := meta_wifi_test.c
LOCAL_C_INCLUDES := $(MTK_PATH_SOURCE)/hardware/meta/common/inc
LOCAL_MODULE := meta_wifi_test
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
LOCAL_ALLOW_UNDEFINED_SYMBOLS := true
LOCAL_SHARED_LIBRARIES := libc libnetutils libcutils liblog libnvram
LOCAL_STATIC_LIBRARIES := libft libmeta_wifi
LOCAL_UNSTRIPPED_PATH := $(TARGET_ROOT_OUT_SBIN_UNSTRIPPED)
include $(MTK_EXECUTABLE)
endif

