LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)

LOCAL_SRC_FILES := meta_clr_emmc.cpp
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)
LOCAL_C_INCLUDES += system/vold
LOCAL_C_INCLUDES += system/core/fs_mgr/include_fstab/fstab
LOCAL_SHARED_LIBRARIES := libcutils
LOCAL_SHARED_LIBRARIES += libc
LOCAL_SHARED_LIBRARIES += liblog
LOCAL_SHARED_LIBRARIES += libselinux
LOCAL_STATIC_LIBRARIES := libstorageutil
LOCAL_STATIC_LIBRARIES += libz
LOCAL_STATIC_LIBRARIES += libft

ifeq ($(MTK_GPT_SCHEME_SUPPORT), yes)
LOCAL_CFLAGS += -DMTK_GPT_SCHEME_SUPPORT
endif
ifeq ($(MNTL_SUPPORT), yes)
LOCAL_CFLAGS += -DMNTL_SUPPORT
endif
ifeq ($(MTK_GENERIC_HAL), yes)
LOCAL_CFLAGS += -DMTK_GENERIC_HAL
endif

LOCAL_MODULE := libmeta_clr_emmc
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
include $(MTK_STATIC_LIBRARY)
