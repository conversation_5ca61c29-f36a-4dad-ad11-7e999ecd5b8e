/*******************************************************************************
** Copyright (c) 2006 MediaTek Inc.
**
** All rights reserved. Copying, compilation, modification, distribution
** or any other use whatsoever of this material is strictly prohibited
** except in accordance with a Software License Agreement with
** MediaTek Inc.
********************************************************************************
*/

#include <sys/types.h>
#include <sys/ioctl.h>
#include <stdio.h>
#include <math.h>
#include <errno.h>
#include <fcntl.h>
#include <ctype.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netdb.h>
//#include <net/ethernet.h>
#include <sys/time.h>
#include <unistd.h>
#include <linux/socket.h>
#include <linux/if.h>
#include <net/if_arp.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/if_ether.h>
#include <linux/types.h>
#ifndef __user
#define __user
#endif
#include <linux/wireless.h>
#include "iwlibstub.h"
#include "meta_wifi_para.h"

int openNetHandle(void)
{
    int sock; /* generic raw socket desc. */


    /* Try to open the socket, if success returns it */
    sock = socket(AF_INET, SOCK_DGRAM, 0);

    if (sock >= 0)
        return sock;
    else
        return -1;
}

void closeNetHandle(int skfd)
{
    /* Close the socket. */
    close(skfd);
}

int enumNetIf(int skfd, _enum_handler fn, void* argc)
{
    struct  ifconf ifc;
    char    buff[1024];
    struct  ifreq *ifr;
    int     i, num = 0;

    ifc.ifc_len = sizeof(buff);
    ifc.ifc_buf = buff;

    if (ioctl(skfd, SIOCGIFCONF, &ifc) < 0)
    {
        fprintf(stderr, "SIOCGIFCONF: %s\n", strerror(errno));
        return num;
    }

    ifr = ifc.ifc_req;

    for (i = ifc.ifc_len / sizeof(struct ifreq); --i >= 0; ifr++)
    {
        (*fn)(skfd, ifr->ifr_name, argc);
        num ++;
    }

    return num;
}


int setIWreq(int skfd, char* if_name,
             unsigned int ndisOid,
             unsigned char* ndisData,
             unsigned int bufLen,
             unsigned int* outputBufLen)
{
    struct iwreq wrq;
    unsigned char *buffer = NULL;
    NDIS_TRANSPORT_STRUCT rNdisStrcut ;
    int result;

    if (if_name == NULL) {
        DBG("<Fail> if_name null pointer detected..\n");
        return -1;
    }
    if (outputBufLen == NULL) {
        DBG("<Fail> outputBufLen null pointer detected..\n");
        return -1;
    }
    if (ndisData == NULL) {
        DBG("<Fail> ndisData null pointer detected..\n");
        return -1;
    }

    DBG("if_name:%s, ndisOid:%x, bufLen=%d\n", if_name, ndisOid, bufLen);

    rNdisStrcut.ndisOidCmd = ndisOid;
    rNdisStrcut.inNdisOidlength = bufLen;

    /* IMPORTANT!! copy input data to buffer (ex. mcr index) */
    if (bufLen > sizeof(rNdisStrcut.ndisOidContent)) {/*avoid OOB access*/
        DBG("<Fail> bufLen more than ndisOidContent size, bufLen : %d, ndisOidContent size: %d", bufLen, sizeof(rNdisStrcut.ndisOidContent));
        return -1;
    }
    memcpy(rNdisStrcut.ndisOidContent, ndisData, bufLen);

    /* Try to read the results */
    wrq.u.data.pointer = &rNdisStrcut;
    wrq.u.data.flags = PRIV_CMD_OID;
    wrq.u.data.length = sizeof(NDIS_TRANSPORT_STRUCT);

    /* Set device name */
    strncpy(wrq.ifr_name, if_name, IFNAMSIZ - 1);
    /* Do the request */
    result = ioctl(skfd, IOCTL_SET_STRUCT, &wrq);

    if (result != IOCTL_RET_SUCCESS) {
        DBG("setIWreq result %s %d %s\n", if_name, result, strerror(errno));
        return -1;
    }

    *outputBufLen = rNdisStrcut.outNdisOidLength;

    DBG("<OK> outputBufLen = %d\n", *outputBufLen);

    return 0;
}

int getIWreq(int skfd, char* if_name,
             unsigned int ndisOid,
             unsigned char* ndisData,
             unsigned int bufLen,
             unsigned int* outputBufLen)
{
    struct iwreq wrq;
    unsigned char *buffer = NULL;
    NDIS_TRANSPORT_STRUCT rNdisStrcut ;
    int result;

    if (if_name == NULL) {
        DBG("<Fail> if_name null pointer detected..\n");
        return -1;
    }
    if (outputBufLen == NULL) {
        DBG("<Fail> outputBufLen null pointer detected..\n");
        return -1;
    }
    if (ndisData == NULL) {
        DBG("<Fail> ndisData null pointer detected..\n");
        return -1;
    }

    DBG("if_name:%s, ndisOid:%x, bufLen=%d\n", if_name, ndisOid, bufLen);

    rNdisStrcut.ndisOidCmd = ndisOid;
    rNdisStrcut.inNdisOidlength = bufLen;

    /* IMPORTANT!! copy input data to buffer (ex. mcr index) */
    if (bufLen > sizeof(rNdisStrcut.ndisOidContent)) {/*avoid OOB access*/
        DBG("<Fail> bufLen more than ndisOidContent size, bufLen : %d, ndisOidContent size: %d", bufLen, sizeof(rNdisStrcut.ndisOidContent));
        return -1;
    }
    memcpy(rNdisStrcut.ndisOidContent, ndisData, bufLen);

    /* Try to read the results */
    wrq.u.data.pointer = &rNdisStrcut;
    wrq.u.data.flags = PRIV_CMD_OID;
    wrq.u.data.length = sizeof(NDIS_TRANSPORT_STRUCT);

    /* Set device name */
    strncpy(wrq.ifr_name, if_name, IFNAMSIZ - 1);
    /* Do the request */
    result = ioctl(skfd, IOCTL_GET_STRUCT, &wrq);

    if (result != IOCTL_RET_SUCCESS) {
        DBG("getIWreq result %s %d %s\n", if_name, result, strerror(errno));
        return -1;
    }

    /*IOCTL success, memcpy return buf data*/
    if (bufLen >= rNdisStrcut.outNdisOidLength) {
        memcpy(ndisData, rNdisStrcut.ndisOidContent, rNdisStrcut.outNdisOidLength);
    }
    else {
        DBG("<Fail> rNdisStrcut.outNdisOidLength :%d more than bufLen:%d\n", rNdisStrcut.outNdisOidLength, bufLen);
    }
    *outputBufLen = rNdisStrcut.outNdisOidLength;

    DBG("<OK> outputBufLen = %d\n", *outputBufLen);

    return 0;
}

/**
 * Function:
 * Send the parsed command to driver
 * for example : set_mcr 0x820F4020 0x14141414
 *
 * Parameters:
 * IN  int    skfd: socket handle
 * IN  char*  if_name: network device name, wlan0
 * IN  char*  pDataCmd: command string from script file
 * IN  int    bufLen: strlen of command string
 * OUT int    outputBufLen: length of output buffer
 *
 * Return:
 *  -1 means error, 0 is success
 */
int driverIWreq(int skfd, char* if_name,
                char* pDataCmd,
                unsigned int bufLen,
                unsigned int* outputBufLen)
{
    struct iwreq wrq;
    char *buffer = NULL;

    int     result;


    buffer = (char *)malloc(WIFI_SCRIPT_TOTAL_BUF_LEN);

    if (buffer == NULL)
        return -1;

    /*copy driver command*/
    memcpy(buffer, pDataCmd, bufLen);
    buffer[bufLen] = '\0';

    /* Try to read the results */
    wrq.u.data.pointer = buffer;
    wrq.u.data.length = bufLen; /*available data length*/

    DBG("input (%d) >> %s\n", wrq.u.data.length, wrq.u.data.pointer);

    /* Set device name */
    strncpy(wrq.ifr_name, if_name, IFNAMSIZ - 1);
    /* Do the request */
    result = ioctl(skfd, IOCTL_GET_DRIVER, &wrq);

    if (wrq.u.data.length > WIFI_SCRIPT_TOTAL_BUF_LEN)
        wrq.u.data.length = WIFI_SCRIPT_TOTAL_BUF_LEN;

    if (result < 0)
        DBG("driverIWreq result %s %d %s\n", if_name, result, strerror(errno));
    else if (result == 0)
    {
        memcpy(pDataCmd, buffer, wrq.u.data.length);
        pDataCmd[wrq.u.data.length] = '\0';

        DBG("output (%d) >> %s\n", wrq.u.data.length, pDataCmd);
        *outputBufLen = wrq.u.data.length;
    }

    free(buffer);

    if (result == 0)
        return 0;

    return -1;
}

int HQAIWreq(int skfd, char* if_name,
             char* pDataCmd,
             unsigned int cmdLen,
             unsigned int* outputBufLen)
{
    struct iwreq wrq;
    int     result, intI, dataLen;

    /* Try to read the results */
    wrq.u.data.pointer = pDataCmd;
    wrq.u.data.length = cmdLen;

    dataLen = (pDataCmd[8] << 8) + pDataCmd[9];
    DBG("Cmd Type: %02x%02x, Cmd ID: %02x%02x Length: %d\n", pDataCmd[4], pDataCmd[5], pDataCmd[6], pDataCmd[7], dataLen);

    if (dataLen > WIFI_SCRIPT_TOTAL_BUF_LEN) { // avoid OOB Read
        DBG("<Fail> dataLen :%d more than buf size:%d \n", wrq.u.data.length, WIFI_SCRIPT_TOTAL_BUF_LEN);
        return -1;
    }

    for (intI = 0; intI < dataLen;)
    {
        DBG("Cmd Content [%d~%d]: %x %x %x %x ", intI, intI + 3, pDataCmd[12 + intI], pDataCmd[13 + intI], pDataCmd[14 + intI], pDataCmd[15 + intI]);  /* content start index in pDataCmd[8], pDataCmd[6,7] is Seq */
        intI = intI + 4;
    }

    /* Set device name */
    strncpy(wrq.ifr_name, if_name, IFNAMSIZ - 1);
    /* Do the request */
#ifdef PETRUS_META_WORKAROUND
    result = ioctl(skfd, IOCTL_QA_TOOL_DAEMON_NEW, &wrq);
#else
    result = ioctl(skfd, IOCTL_QA_TOOL_DAEMON, &wrq);
#endif

    if (result < 0)
        DBG("HQAIWreq result %s %d %s\n", if_name, result, strerror(errno));
    else if (result == 0)
    {
        if (wrq.u.data.length > WIFI_SCRIPT_TOTAL_BUF_LEN) { // avoid OOB Read
            DBG("<Fail> wrq.u.data.length :%d more than buf size:%d \n", wrq.u.data.length, WIFI_SCRIPT_TOTAL_BUF_LEN);
            return -1;
        }
        for (intI = 0; intI < wrq.u.data.length;)
        {
            DBG("Rsp Content [%d~%d]: %x %x %x %x ", intI, intI + 3, pDataCmd[intI], pDataCmd[1 + intI], pDataCmd[2 + intI], pDataCmd[3 + intI]);
            intI = intI + 4;
        }
        *outputBufLen = wrq.u.data.length;
    }

    if (result == 0)
        return 0;

    return -1;
}