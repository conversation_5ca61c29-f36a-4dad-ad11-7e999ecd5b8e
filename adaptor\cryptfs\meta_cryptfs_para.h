/* Copyright Statement:
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to MediaTek Inc. and/or its licensors.
 * Without the prior written permission of MediaTek inc. and/or its licensors,
 * any reproduction, modification, use or disclosure of MediaTek Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 */
/* MediaTek Inc. (C) 2010. All rights reserved.
 *
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON>E<PERSON> SOFTWARE")
 * RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO RECEIVER ON
 * AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH THE MEDIATEK SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN MEDIATEK SOFTWARE. MEDIATEK SHALL ALSO NOT BE RESPONSIBLE FOR ANY MEDIATEK
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
 *
 * The following software/firmware and/or related documentation ("MediaTek Software")
 * have been modified by MediaTek Inc. All revisions are subject to any receiver's
 * applicable license agreements with MediaTek Inc.
 */

/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2008
*
*  BY OPENING THIS FILE, BUYER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
*  THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("MEDIATEK SOFTWARE")
*  RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO BUYER ON
*  AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
*  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
*  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
*  NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
*  SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
*  SUPPLIED WITH THE MEDIATEK SOFTWARE, AND BUYER AGREES TO LOOK ONLY TO SUCH
*  THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. MEDIATEK SHALL ALSO
*  NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE RELEASES MADE TO BUYER'S
*  SPECIFICATION OR TO CONFORM TO A PARTICULAR STANDARD OR OPEN FORUM.
*
*  BUYER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND CUMULATIVE
*  LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
*  AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
*  OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY BUYER TO
*  MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
*
*  THE TRANSACTION CONTEMPLATED HEREUNDER SHALL BE CONSTRUED IN ACCORDANCE
*  WITH THE LAWS OF THE STATE OF CALIFORNIA, USA, EXCLUDING ITS CONFLICT OF
*  LAWS PRINCIPLES.  ANY DISPUTES, CONTROVERSIES OR CLAIMS ARISING THEREOF AND
*  RELATED THERETO SHALL BE SETTLED BY ARBITRATION IN SAN FRANCISCO, CA, UNDER
*  THE RULES OF THE INTERNATIONAL CHAMBER OF COMMERCE (ICC).
*
*****************************************************************************/
/*****************************************************************************
 *
 * Filename:
 * ---------
 *   meta_cryptfs_para.h
 *
 * Project:
 * --------
 *   ALPS
 *
 * Description:
 * ------------
 *  CRYPTFS meta data struct define.
 *
 * Author:
 * -------
 *
 *
 *============================================================================
 *             HISTORY
 * Below this line, this part is controlled by CC/CQ. DO NOT MODIFY!!
 *------------------------------------------------------------------------------
 * $Revision:$
 * $Modtime:$
 * $Log:$
 *
 *
 *------------------------------------------------------------------------------
 * Upper this line, this part is controlled by CC/CQ. DO NOT MODIFY!!
 *============================================================================
 ****************************************************************************/
#ifndef __META_CRYPTFS_H_
#define __META_CRYPTFS_H_


#ifdef __cplusplus
extern "C" {
#endif

typedef enum{
     CRYPTFS_OP_QUERY_STATUS = 0,
     CRYPTFS_OP_VERIFY,
     CRYPTFS_OP_SET_CFG,
     CRYPTFS_OP_GET_CFG,
     CRYPTFS_END
}CRYPTFS_OP;

typedef struct
{
    unsigned char  sign;   // No means
} CRYPTFS_QUERY_STATUS_REQ;

typedef struct
{
  unsigned char   status;    // 1 means encrypted; 0 means decrypted or not supported
} CRYPTFS_QUERY_STATUS_CNF;

typedef struct
{
   unsigned char pwd[32];
   int  length;
} CRYPTFS_VERIFY_REQ;

typedef struct
{
   unsigned char   decrypt_result;   // 1 means decrypt successfully, 0 means decrypt fail
} CRYPTFS_VERIFY_CNF;

typedef struct
{
    unsigned char cfg;   /* 0 means to disable 'default encryption'
                                            1 means to enable 'default encryption' */
} CRYPTFS_SET_CFG_REQ;

typedef struct
{
  unsigned char set_cfg_result; /* 1 means to set cfg successfuly
                                                        0 means that failed to set cfg */
} CRYPTFS_SET_CFG_CNF;


typedef struct
{
    unsigned char  sign;   // No means
} CRYPTFS_GET_CFG_REQ;

typedef struct
{
  unsigned char get_cfg_result; /*
                                                       0 means to disable 'default encryption'
                                                       1 means to enable 'default encryption'
                                                       others(ex, 'F') means Failure that no such cfg or something wrong
                                                     */
} CRYPTFS_GET_CFG_CNF;


typedef union
{
   CRYPTFS_QUERY_STATUS_CNF     query_status_cnf;
   CRYPTFS_VERIFY_CNF   verify_cnf;
   CRYPTFS_SET_CFG_CNF  set_cfg_cnf;
   CRYPTFS_GET_CFG_CNF  get_cfg_cnf;
} FT_CRYPTFS_RESULT;

typedef union
{
    CRYPTFS_QUERY_STATUS_REQ     query_statust_req;
    CRYPTFS_VERIFY_REQ   verify_req;
    CRYPTFS_SET_CFG_REQ  set_cfg_req;
    CRYPTFS_GET_CFG_REQ  get_cfg_req;
} FT_CRYPTFS_CMD;

typedef struct {
     FT_H	    header;
     CRYPTFS_OP     op;
     unsigned char  m_status;   // The data frame state, 0 means normal
     FT_CRYPTFS_RESULT  result;
} FT_CRYPTFS_CNF;

typedef struct {
    FT_H	       header;
    CRYPTFS_OP    op;
    FT_CRYPTFS_CMD  cmd;
} FT_CRYPTFS_REQ;

bool META_CRYPTFS_init();
void META_CRYPTFS_deinit();
void META_CRYPTFS_OP(FT_CRYPTFS_REQ *req);

#ifdef __cplusplus
};
#endif

#endif

