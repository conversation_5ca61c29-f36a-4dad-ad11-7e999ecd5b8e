LOCAL_PATH:=$(call my-dir)
include $(CLEAR_VARS)
#LOCAL_ARM_MODE:=arm
LOCAL_SHARED_LIBRARIES:= libc \
  libnvram \
  liblog \
  libcutils \

LOCAL_STATIC_LIBRARIES += libft

LOCAL_SRC_FILES:=Meta_APEditor_Para.c
LOCAL_HEADER_LIBRARIES += libnvram_headers
LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)
LOCAL_MODULE:=libmeta_apeditor
LOCAL_PROPRIETARY_MODULE := true
LOCAL_MODULE_OWNER := mtk
include $(MTK_STATIC_LIBRARY)


