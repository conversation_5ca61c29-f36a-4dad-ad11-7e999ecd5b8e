This directory contains BT Meta mode test library


WHAT IT DOES?
=============
It provide BT test feature on meta mode, including BT power on/off, write/read and some other functions.

HOW IT WAS BUILT?
==================

It needs the following libs from AOSP:
1. libc.so
2. libcutils
3. libnetutils

and the following libs from MediaTek:
1. libft.a


HOW TO USE IT?
==============

Files in this directory is used to
generate a library which's name is 'libmeta_bluetooth'


libmeta_bluetooth
The lib  'libmeta_bluetooth' is loaded when target enter meta mode,
Meta main thread will call META BT APIs, if meta gps tool is launched.


All the source code of this library were written by MediaTek co..

