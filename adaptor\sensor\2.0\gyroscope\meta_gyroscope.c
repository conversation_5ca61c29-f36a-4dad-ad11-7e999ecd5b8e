#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <time.h>
//#include <linux/mtgpio.h>
#include "meta_gyroscope.h"
#include <log/log.h>
#include <HfManagerWrapper.h>
#include "HfSensorType.h"

#define GYRO_PREFIX   "[Meta] GYRO: "
#define MGYROLOGD(fmt, arg...) ALOGD(GYRO_PREFIX fmt, ##arg)
#define MGYROLOGE(fmt, arg...) ALOGE(GYRO_PREFIX fmt, ##arg)
#define MIN_GYRO_SUPOORT_DELAY_NS 2500000 //400hz
#define UNIT_SEC_TO_NS 1.0e9
#define UNIT_MS_TO_NS 1000000
#define SAMPLE_RAWDATA_PERIOD_MS 5 * UNIT_MS_TO_NS
#define TIMEOUT_MS 3000
#define TIMEOUT_SEC 3

static void *hf_manager = NULL;
static void *hf_looper = NULL;
static GYRO_CNF_CB meta_gyro_cb = NULL;

void Meta_Gyroscope_Register(GYRO_CNF_CB cb)
{
    meta_gyro_cb = cb;
}

bool Meta_Gyroscope_Open(void)
{
    hf_manager = HfManagerCreate();
    hf_looper = HfLooperCreate(HfManagerGetFd(hf_manager), 64);

    if (HfManagerFindSensor(hf_manager, SENSOR_TYPE_GYROSCOPE) < 0) {
        MGYROLOGE("gyroscope open fail!\n");
        goto err_out;
    }

    return true;

err_out:
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return false;
}

static int gyro_exec_cali(GYRO_CMD_CALI* arg, GYRO_ACK_CALI *cali)
{
    sensors_event_t data[32] = {0};
    int8_t ret = -1;
    int64_t delay_ns = 0;
    struct timespec start_time, cur_time;
    float delta_time = 0.0f;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));

    if (!arg || !cali) {
        MGYROLOGE("arg/cali is null, return!\n");
        return ret;
    }
    delay_ns = (int64_t)arg->delay * UNIT_MS_TO_NS;
    if (delay_ns < MIN_GYRO_SUPOORT_DELAY_NS)
        delay_ns = MIN_GYRO_SUPOORT_DELAY_NS;

    HfManagerRequestFactoryCalibration(hf_manager, SENSOR_TYPE_GYROSCOPE, true);
    HfManagerEnableFactoryCalibration(hf_manager, SENSOR_TYPE_GYROSCOPE);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_GYROSCOPE, delay_ns, 0);
    clock_gettime(CLOCK_REALTIME, &start_time);

    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MGYROLOGE("eventlooper error:%d\n", err);
            ret = -1;
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == CALI_ACTION) {
                MGYROLOGD("calibration result:%f,%f,%f,%d\n",
                    data[i].gyro.x, data[i].gyro.x, data[i].gyro.x, data[i].gyro.status);
                cali->x = (int)data[i].gyro.x;
                cali->y = (int)data[i].gyro.y;
                cali->z = (int)data[i].gyro.z;
                ret = data[i].gyro.status;
                goto exit;
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerRequestFactoryCalibration(hf_manager, SENSOR_TYPE_GYROSCOPE, false);
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_GYROSCOPE);
    return ret;
}

static int gyro_exec_read_raw(GYRO_ACK_READ_RAW *raw)
{
    sensors_event_t data[32] = {0};
    int8_t ret = -1;
    struct timespec start_time, cur_time;
    float delta_time = 0.0f;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));

    if (!raw) {
        MGYROLOGE("raw is null, return!\n");
        return -1;
    }

    HfManagerEnableRawData(hf_manager, SENSOR_TYPE_GYROSCOPE);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_GYROSCOPE, SAMPLE_RAWDATA_PERIOD_MS, 0);
    clock_gettime(CLOCK_REALTIME, &start_time);

    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MGYROLOGE("eventLooper err:%d\n", err);
            ret = -1;
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == RAW_ACTION) {
                raw->x = (int)data[i].data[0];
                raw->y = (int)data[i].data[1];
                raw->z = (int)data[i].data[2];
                MGYROLOGD("raw data:%d, %d,%d\n", raw->x, raw->y, raw->z);
                ret = 0;
                goto exit;
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_GYROSCOPE);
    HfManagerDisableRawData(hf_manager, SENSOR_TYPE_GYROSCOPE);
    return ret;
}

void Meta_Gyroscope_OP(GYRO_REQ *req)
{
    GYRO_CNF cnf;

    memset(&cnf, 0, sizeof(GYRO_CNF));
    cnf.header.id = FT_GYROSCOPE_CNF_ID;
    cnf.header.token = req->header.token;
    cnf.op = req->op;
    switch (req->op) {
        case GYRO_OP_CALI:
            cnf.gyro_err = gyro_exec_cali(&req->cmd.cali, &cnf.ack.cali);
            if (cnf.gyro_err)
                cnf.status = META_FAILED;
            else
                cnf.status = META_SUCCESS;
            break;

        case GYRO_OP_READ_RAW:
            cnf.gyro_err = gyro_exec_read_raw(&cnf.ack.readraw);
            if (cnf.gyro_err)
                cnf.status = META_FAILED;
            else
                cnf.status = META_SUCCESS;
            break;

        default:
            cnf.gyro_err = -EINVAL;
            cnf.status = META_FAILED;
            MGYROLOGE("unknown OP: %d\n", req->op);
            break;
    }

    if (meta_gyro_cb)
        meta_gyro_cb(&cnf);
    else
        WriteDataToPC(&cnf, sizeof(GYRO_CNF), NULL, 0);
}

bool Meta_Gyroscope_Close(void)
{
    MGYROLOGD("Meta_Gyroscope_Close");
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return true;
}

