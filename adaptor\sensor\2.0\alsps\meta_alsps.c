#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <time.h>
//#include <linux/mtgpio.h>
#include <linux/sensors_io.h>
#include <log/log.h>
#include "meta_alsps.h"
#include <HfManagerWrapper.h>
#include "HfSensorType.h"

#define ALSPS_PREFIX   "[Meta] ALSPS: "
#define MALSPSLOGD(fmt, arg ...) ALOGD(ALSPS_PREFIX fmt, ##arg)
#define MALSPSLOGE(fmt, arg ...) ALOGE(ALSPS_PREFIX fmt, ##arg)
#define UNIT_SEC_TO_NS 1.0e9
#define UNIT_MS_TO_NS 1000000
#define SAMPLE_RAWDATA_PERIOD_MS 200 * UNIT_MS_TO_NS
#define TIMEOUT_MS 1200 //PC will report error if cannot receive data unit 1200ms
#define TIMEOUT_SEC 1.2

static void *hf_manager = NULL;
static void *hf_looper = NULL;

bool Meta_ALSPS_Open(void)
{
    hf_manager = HfManagerCreate();
    hf_looper = HfLooperCreate(HfManagerGetFd(hf_manager), 64);

    if (HfManagerFindSensor(hf_manager, SENSOR_TYPE_LIGHT) < 0) {
        MALSPSLOGE("als open fail!\n");
        goto err_out;
    }

    if (HfManagerFindSensor(hf_manager, SENSOR_TYPE_PROXIMITY) < 0) {
        MALSPSLOGE("ps open fail!\n");
        goto err_out;
    }
    return true;

err_out:
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return false;
}

int8_t alsps_exec_read_raw()
{
    sensors_event_t data[32] = {0};
    int8_t ret = -1;
    bool get_als_raw_success = false, get_ps_raw_success = false;
    float delta_time = 0.0f;
    struct timespec start_time, cur_time;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));
    HfManagerEnableRawData(hf_manager, SENSOR_TYPE_LIGHT);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_LIGHT, SAMPLE_RAWDATA_PERIOD_MS, 0);
    HfManagerEnableRawData(hf_manager, SENSOR_TYPE_PROXIMITY);
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_PROXIMITY, SAMPLE_RAWDATA_PERIOD_MS, 0);
    clock_gettime(CLOCK_REALTIME, &start_time);
    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MALSPSLOGE("eventLooper err:%d\n", err);
            ret = -1;
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == RAW_ACTION) {
                MALSPSLOGD("raw data:%d,%f\n", data[i].type, data[i].data[0]);
                switch (data[i].type) {
                    case SENSOR_TYPE_LIGHT:
                        get_als_raw_success = true;
                        break;
                    case SENSOR_TYPE_PROXIMITY:
                        get_ps_raw_success = true;
                }
                if (get_als_raw_success && get_ps_raw_success) {
                    ret = 0;
                    goto exit;
                }
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_LIGHT);
    HfManagerDisableRawData(hf_manager, SENSOR_TYPE_LIGHT);
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_PROXIMITY);
    HfManagerDisableRawData(hf_manager, SENSOR_TYPE_PROXIMITY);
    return ret;
}

int Meta_ALSPS_OP()
{
    int8_t err = -1;
    err = alsps_exec_read_raw();
    return err;
}

bool Meta_ALSPS_Close(void)
{
    MALSPSLOGD("Meta_ALSPS_Close\n");
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return true;
}
