/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2008
*
*  BY OPENING THIS FILE, BUYER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
*  THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
*  RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO BUYER ON
*  AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
*  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
*  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
*  NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
*  SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
*  SUPPLIED WITH THE MEDIATEK SOFTWARE, AND BUYER AGREES TO LOOK ONLY TO SUCH
*  THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. MEDIATEK SHALL ALSO
*  NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE RELEASES MADE TO BUYER'S
*  SPECIFICATION OR TO CONFORM TO A PARTICULAR STANDARD OR OPEN FORUM.
*
*  BUYER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND CUMULATIVE
*  LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
*  AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
*  OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY BUYER TO
*  MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
*
*  THE TRANSACTION CONTEMPLATED HEREUNDER SHALL BE CONSTRUED IN ACCORDANCE
*  WITH THE LAWS OF THE STATE OF CALIFORNIA, USA, EXCLUDING ITS CONFLICT OF
*  LAWS PRINCIPLES.  ANY DISPUTES, CONTROVERSIES OR CLAIMS ARISING THEREOF AND
*  RELATED THERETO SHALL BE SETTLED BY ARBITRATION IN SAN FRANCISCO, CA, UNDER
*  THE RULES OF THE INTERNATIONAL CHAMBER OF COMMERCE (ICC).
*
*****************************************************************************/

//
// Copyright (c) Microsoft Corporation.  All rights reserved.
//
//
// Use of this source code is subject to the terms of the Microsoft end-user
// license agreement (EULA) under which you licensed this SOFTWARE PRODUCT.
// If you did not accept the terms of the EULA, you are not authorized to use
// this source code. For a copy of the EULA, please see the LICENSE.RTF on your
// install media.
//

/*****************************************************************************
 *
 * Filename:
 * ---------
 *   meta_wifi_para.h
 *
 * Project:
 * --------
 *   DUMA
 *
 * Description:
 * ------------
 *   the defination of Wi-Fi wrapper interface for META FT task.
 *
 * Author:
 * -------
 *  Renbang Jiang (MTK80150)
 *
 *============================================================================
 *             HISTORY
 * Below this line, this part is controlled by CC/CQ. DO NOT MODIFY!!
 *------------------------------------------------------------------------------
 * $Revision:$
 * $Modtime:$
 * $Log:$
 *
 * Mar 6 2009 mtk80150
 * [DUMA00110922] [Wi-Fi] Wi-Fi driver for META initial timeout
 * Add Timeout for driver initializing
 *
 * Mar 6 2009 mtk80150
 * [DUMA00110922] [Wi-Fi] Wi-Fi driver for META initial timeout
 * Add timeout for Wi-Fi driver initialize
 *
 * Feb 22 2009 mtk80150
 * [DUMA00109732] [Wi-Fi] Driver version update to 1.13
 *
 *
 *
 *------------------------------------------------------------------------------
 * Upper this line, this part is controlled by CC/CQ. DO NOT MODIFY!!
 *============================================================================
 ****************************************************************************/

#ifndef _META_WIFI_PARA_H_
#define _META_WIFI_PARA_H_

#include <sys/types.h>
#include "MetaPub.h"
#include <cutils/log.h>
#include <type.h>

#define CONNAC_SOC3_0                   0x3000
#define CONNAC_SOC2_0                   0x2000
#define CONNAC_SOC7_0                   0x7000
#define SUPPORT_SOC3_0_DNL_VER          0x3030


#define DRIVER_INIT_TIMEOUT 1000

#define ZONE_ERROR 1
#define ZONE_FUNC 0

#define FUNCTION_CODE_QUERY_OID_VALUE           0x201
#define FUNCTION_CODE_SET_OID_VALUE             0x205
#define FUNCTION_CODE_POSTINIT_VALUE            0x209
#define NVRAM_READ  FALSE
#define NVRAM_WRITE TRUE

#define WF0           0
#define WF1           1
#define WF_NUM        2

#define META_WIFI_STATUS_SUCCESS         0
#define META_WIFI_STATUS_FAIL           -1
#define META_WIFI_STATUS_INVALID_PARA   -2
#define META_WIFI_STATUS_NOT_SUPPORT    -3


#define _META_CTL_CODE(_Function, _Method, _Access)                \
            CTL_CODE(FILE_DEVICE_NETWORK, _Function, _Method, _Access)

#define IOCTL_META_SET_OID_VALUE                                   \
            _META_CTL_CODE(FUNCTION_CODE_SET_OID_VALUE,            \
                           METHOD_BUFFERED,                        \
                           FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_META_QUERY_OID_VALUE                                 \
            _META_CTL_CODE(FUNCTION_CODE_QUERY_OID_VALUE,          \
                           METHOD_BUFFERED,                        \
                           FILE_READ_ACCESS | FILE_WRITE_ACCESS)


#define IOCTL_META_WIFI_POSTINIT                                        \
            _META_CTL_CODE(FUNCTION_CODE_POSTINIT_VALUE,           \
                           METHOD_BUFFERED,                        \
                           FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define FREEIF(p)   do { if(p) free(p); p = NULL; } while(0)



#define WIFI_DEV_NAME (L"NDL1:")
#define WIFI_READY_EVENT_NAME (L"OEM/WiFiDriverReady")


#define HQA_CMD_MAGIC_NO 0x18142880
#define HQA_CMD_OPEN_ADAPTER 0x1000
#define HQA_CMD_CLOSE_ADAPTER 0x1001
#define HQA_CMD_SetTxPath     0x100B
#define HQA_CMD_DoCalibrationTestItem  0x150A
#define HQA_CMD_GetDumpRecal           0x1581

#define HQA_CMD_SetTxPowerExt 0x1011
#define HQA_CMD_EXTEND  0x1600
#define HQA_CMD_DBDCSetChannel  0x01
#define HQA_CMD_DBDCStartTx 0x03
#define HQA_CMD_DBDCStopTx 0x05

#define HQA_CAL_ITEM_DNL            0x00004000
#define DNL_WF_PATH_CR_NUM          16 /* CR:32*(WF0+WF1)*/

#define HQA_CAL_ITEM_LNA_GIAN_CAL   0x00008000
#define LNA_GIAN_CAL_WF_PATH_CR_NUM          2 /*CR:2 *(WF0+WF1)*/

#define TSSI_CH_OFS_GT_NUM 6
#define PER_CH_GROUP_IN_RF_GROUP 4 /* 2x(low channel+high channel) */

#define MAX_RECAL_DATA_NUM              64

#define NVRAM_GT2_OFFSET    0x00
#define NVRAM_GT3_OFFSET    0x01
#define NVRAM_GT4_OFFSET    0x02
#define NVRAM_GT5_OFFSET    0x03
#define NVRAM_GT6_OFFSET    0x04
#define NVRAM_GT7_OFFSET    0x05

#define NDL_OFFSET_BIAS0_MASK 0xFF000000
#define NDL_OFFSET_BIAS0_SHFT 24

#define NDL_OFFSET_BIAS1_MASK 0x00FF0000
#define NDL_OFFSET_BIAS1_SHFT 16

#define NDL_OFFSET_BIAS2_MASK 0x0000FF00
#define NDL_OFFSET_BIAS2_SHFT 8

#define NDL_OFFSET_BIAS3_MASK 0x000000FF
#define NDL_OFFSET_BIAS3_SHFT 0

#define NDL_OFFSET_BIAS4_MASK 0xFF000000
#define NDL_OFFSET_BIAS4_SHFT 24

#define NDL_OFFSET_BIAS5_MASK 0x00FF0000
#define NDL_OFFSET_BIAS5_SHFT 16


#define LNA_GAIN_TAB0_MASK 0x7F000000  //[30..24]
#define LNA_GAIN_TAB0_SHFT 24

#define LNA_GAIN_TAB1_MASK 0x007F0000  //[22..16]
#define LNA_GAIN_TAB1_SHFT 16

#define LNA_GAIN_TAB2_MASK 0x00007F00  //[14..8]
#define LNA_GAIN_TAB2_SHFT 8

#define LNA_GAIN_TAB3_MASK 0x0000007F  //[6..0]
#define LNA_GAIN_TAB3_SHFT 0

#define LNA_GAIN_TAB4_MASK 0x7F000000  //[30..24]
#define LNA_GAIN_TAB4_SHFT 24


/* This macro returns the byte offset of a named field in a known structure
 *   type.
 *   _type - structure name,
 *   _field - field name of the structure
 */
#ifndef OFFSET_OF
#define OFFSET_OF(_type, _field)    offsetof(_type, _field)
#endif /* OFFSET_OF */
#define SIGNED_CONVERT_EXTEND_BITS(data,ori_res) ((unsigned)(data) >= (unsigned)BIT((ori_res) - 1) ? (signed)((data)- BIT((ori_res))) : (signed)(data))


#ifdef LOG_TAG
#undef  LOG_TAG
#endif
#define LOG_TAG         "WIFI_META "
#define WIFI_META_VER   "20191021_0"

#define WIFI_META_TEST_DEBUG 0
#if (WIFI_META_TEST_DEBUG == 1)
#define DBG(f, ...)     printf(f, ##__VA_ARGS__)
#define TRC(f, ...)     printf(f, ##__VA_ARGS__)
#define ERR(f, ...)     printf(f, ##__VA_ARGS__)
#define WAN(f, ...)     printf(f, ##__VA_ARGS__)
#else
#define DBG(f, ...)     ALOGD("%s: " f, __func__, ##__VA_ARGS__)
#define TRC(f)          ALOGW("%s #%d", __func__, __LINE__)
#define ERR(f, ...)     ALOGE("%s: " f, __func__, ##__VA_ARGS__)
#define WAN(f, ...)     ALOGW("%s: " f, __func__, ##__VA_ARGS__)
#endif


typedef enum
{
    WIFI_CMD_SET_OID = 0,
    WIFI_CMD_QUERY_OID = 1,
    WIFI_CMD_NVRAM_WRITE_ACCESS = 2,
    WIFI_CMD_NVRAM_READ_ACCESS = 3,
    WIFI_CMD_INIT = 4,
    WIFI_CMD_DEINIT = 5,
    WIFI_CMD_SCRIPT = 6,
    WIFI_CMD_HQA = 7,
    WIFI_CMD_PL_CALIBRATION = 8,
    WIFI_CMD_AFC_CALIBRATION = 9,
    WIFI_CMD_NUM
} WIFI_CMD_TYPE;

typedef enum
{
    WIFI_PL_CAL_TX_PWR = 1,
    WIFI_PL_CAL_EPA_FE_GAIN = 2,
    WIFI_PL_CAL_LNA_GAIN_CAL = 3, /*RSSI GAIN CAL*/
    WIFI_PL_CAL_DNL_CAL = 4,
    WIFI_PL_CAL_NUM
} WIFI_PL_CAL_TYPE;

typedef enum
{
    TX_PWR_CAL_ACT_START = 0,
    TX_PWR_CAL_ACT_ADJUST = 1,
    TX_PWR_CAL_ACT_END = 2,
    TX_PWR_CAL_ACT_INTERPOLAT = 3, //Interpolation
    TX_PWR_CAL_ACT_NUM
} TX_PWR_CAL_ACT;


typedef enum
{
    TYPE_INTERPOLATION = 0,
    TYPE_GROUP_THE_SAME = 1,
    TX_PWR_INTERPOLATION_NUM
} TX_PWR_INTERPOLATION_TYPE;

typedef enum
{
    INTER_ACT_INTERPOLATION = 0,
    INTER_ACT_GROUP_THE_SAME = 1,
    INTER_ACT_NOT_SUPPORT = 2,
    INTER_ACT_NUM
} INTER_ACT;


/* This starting freq of the band is unit of kHz */
typedef enum _ENUM_BAND_T
{
    BAND_NULL   = 0000000,
    BAND_2G4    = 2407000,
    BAND_5G     = 5000000,
    BAND_4G9375 = 4937500,
    BAND_4G89   = 4890000,
    BAND_4G85   = 4850000,
    BAND_4G     = 4000000,
    BAND_5G0025 = 5002500,
    BAND_4G0025 = 4002500,
    BAND_6G     = 5950000
} ENUM_BAND_T, *P_ENUM_BAND_T;

/* The following macro to translate channel number to its center freq
 * in unit of kHz
 */
#define CHNL_FREQ_2G(n)         (((n) == 14) ? 2484000 : \
                                     (BAND_2G4 + 5000 * (n)))
#define CHNL_FREQ_5G(n)         (BAND_5G + 5000 * (n))
#define CHNL_FREQ_4G9375(n)     (BAND_4G9375 + 5000 * (n))
#define CHNL_FREQ_4G89(n)       (BAND_4G89 + 5000 * (n))
#define CHNL_FREQ_4G85(n)       (BAND_4G85 + 5000 * (n))
#define CHNL_FREQ_4G(n)         (BAND_4G + 5000 * (n))
#define CHNL_FREQ_5G0025(n)     (BAND_5G0025 + 5000 * (n))
#define CHNL_FREQ_4G0025(n)     (BAND_4G0025 + 5000 * (n))

#define CHNL_FREQ(_eBand, _u1Chnl) \
        (((_eBand) == BAND_2G4 && (_u1Chnl) == 14) ? 2484000 : \
         ((_eBand) + 5000 * (_u1Chnl)))

#define FREQ_BAND(_u4Freq) \
        ((_u4Freq) < BAND_4G ? BAND_2G4 : BAND_5G) /* To do: support more bands */

#define FREQ_CHNL(_eBand, _u4Freq) \
        ((unsigned char)(((_eBand) == BAND_2G4 && (_u4Freq) == 2484000) ? 14 : \
                  (((_u4Freq) - (_eBand)) / 5000)))



typedef enum _ENUM_CBW_DBW_T
{
    CDBW_20,
    CDBW_40,
    CDBW_80,
    CDBW_160,
    CDBW_80P80,
    CDBW_5,
    CDBW_10,
    CDBW_NUM
} ENUM_CBW_DBW_T, *P_ENUM_CBW_DBW_T;

typedef struct
{
    FT_H            header;
    WIFI_CMD_TYPE   type;
    int             dummy;
} FT_WM_WIFI_REQ;

typedef struct
{
    FT_H            header;
    WIFI_CMD_TYPE   type;
    int            drv_status;
    unsigned char   status;
} FT_WM_WIFI_CNF;
typedef struct _INTERPOLATION_CH_BOUND_A_BAND
{
    unsigned char lowBoundCh;
    unsigned char upperBoundCh;
} INTERPOLATION_CH_BOUND_A_BAND, *P_INTERPOLATION_CH_BOUND_A_BAND;


typedef struct _CMD_PL_CAL
{
    unsigned int calId;
    unsigned int action;
    unsigned int flags;
    unsigned int inputLen;
    unsigned int au4Buffer[300];
} CMD_PL_CAL, *P_CMD_PL_CAL;

typedef struct _SET_OID_STRUC
{
    unsigned int  oid;
    unsigned int  dataLen;
    unsigned char data[1];

} SET_OID_STRUC, *PSET_OID_STRUC;


typedef struct _QUERY_OID_STRUC
{
    unsigned int  oid;
    unsigned int  dataLen;
    unsigned char data[1];

} QUERY_OID_STRUC, *PQUERY_OID_STRUC;

typedef struct _NVRAM_ACCESS_STRUCT
{
    unsigned int  dataLen;
    unsigned int  dataOffset;
    unsigned char data[1];
} NVRAM_ACCESS_STRUCT, *PNVRAM_ACCESS_STRUCT;

typedef union
{
    SET_OID_STRUC   SetOidPara;
    QUERY_OID_STRUC QueryOidPara;
} OID_STRUC, *POID_STRUC;

typedef struct _HQA_CMD_FRAME
{
    unsigned int magicNo;
    unsigned short type;
    unsigned short id;
    unsigned short length;
    unsigned short sequence;
    unsigned char data[1];
} HQA_CMD_FRAME, *P_HQA_CMD_FRAME;

typedef struct _HQA_SET_TX_PATH
{
    unsigned int tx_path;
    unsigned int band_idx;
} HQA_SET_TX_PATH, *P_HQA_SET_TX_PATH;

typedef struct _HQA_SET_TX_POWER
{
    unsigned int power;
    unsigned int band_idx;
    unsigned int channel;
    unsigned int channel_band;
    unsigned int ant_idx;
} HQA_SET_TX_POWER, *P_HQA_SET_TX_POWER;


typedef struct _HQA_DO_CAL_TEST_ITEM
{
    unsigned int item;
    unsigned int band_idx;
} HQA_DO_CAL_TEST_ITEM, *P_HQA_DO_CAL_TEST_ITEM;

typedef struct _RECAL_INFO_T
{
    unsigned short status;
    unsigned int u4Count;
    unsigned int u4CalId[MAX_RECAL_DATA_NUM];
    unsigned int u4CalAddr[MAX_RECAL_DATA_NUM];
    unsigned int u4CalValue[MAX_RECAL_DATA_NUM];
} RECAL_INFO_T, *P_RECAL_INFO_T;

typedef struct _HQA_SET_CH
{
    unsigned int ext_id;
    unsigned int num_param;
    unsigned int band_idx;
    unsigned int central_ch0;
    unsigned int central_ch1;
    unsigned int sys_bw;
    unsigned int perpkt_bw;
    unsigned int pri_sel;
    unsigned int reason;
    unsigned int ch_band;
    unsigned int out_band_freq;
} HQA_SET_CH, *P_HQA_SET_CH;


typedef struct _HQA_PARA_INFO
{
    unsigned int wf_idx; /* bit[0]:WF0,bit[1]:WF1,bit[2]:WF0/WF1 */
    unsigned int dbdcBandIdx;
    unsigned int chBand; /*0:2,4G , 1:5G*/
    unsigned int chS1; /* Channel Number in unit of kHz - 20/40/80/160 */
    unsigned int chS2; /* Channel Number in unit of kHz - 80+80*/
    ENUM_CBW_DBW_T eCbw;
    unsigned int power; /* In unit of 0.5 dBm */
} HQA_PARA_INFO, *P_HQA_PARA_INFO;

// temp solution need to sync with NVRAM
typedef struct _WIFI_NVRAM_TSSI_CH_OFS_T{
    unsigned char ucTssiChOfsGT2;
    unsigned char ucTssiChOfsGT3;
    unsigned char ucTssiChOfsGT4;
    unsigned char ucTssiChOfsGT5;
    unsigned char ucTssiChOfsGT6;
    unsigned char ucTssiChOfsGT7;
} WIFI_NVRAM_TSSI_CH_OFS_T, *P_WIFI_NVRAM_TSSI_CH_OFS_T;


typedef struct _WIFI_NVRAM_TX_DNL_T{
    unsigned char ucTxDnlCckGT0;
    unsigned char ucTxDnlCckGT1;
    unsigned char ucTxDnlCckGT2;
    unsigned char ucTxDnlCckGT3;
    unsigned char ucTxDnlCckGT4;
    unsigned char ucTxDnlCckGT5;
    unsigned char ucTxDnlCckGT6;
    unsigned char ucTxDnlCckGT7;
    unsigned char ucTxDnlLowGT0;
    unsigned char ucTxDnlLowGT1;
    unsigned char ucTxDnlLowGT2;
    unsigned char ucTxDnlLowGT3;
    unsigned char ucTxDnlLowGT4;
    unsigned char ucTxDnlLowGT5;
    unsigned char ucTxDnlLowGT6;
    unsigned char ucTxDnlLowGT7;
    unsigned char ucTxDnlMidGT0;
    unsigned char ucTxDnlMidGT1;
    unsigned char ucTxDnlMidGT2;
    unsigned char ucTxDnlMidGT3;
    unsigned char ucTxDnlMidGT4;
    unsigned char ucTxDnlMidGT5;
    unsigned char ucTxDnlMidGT6;
    unsigned char ucTxDnlMidGT7;
    unsigned char ucTxDnlHighGT0;
    unsigned char ucTxDnlHighGT1;
    unsigned char ucTxDnlHighGT2;
    unsigned char ucTxDnlHighGT3;
    unsigned char ucTxDnlHighGT4;
    unsigned char ucTxDnlHighGT5;
    unsigned char ucTxDnlHighGT6;
    unsigned char ucTxDnlHighGT7;
    unsigned char ucTxDnlUltraGT0;
    unsigned char ucTxDnlUltraGT1;
    unsigned char ucTxDnlUltraGT2;
    unsigned char ucTxDnlUltraGT3;
    unsigned char ucTxDnlUltraGT4;
    unsigned char ucTxDnlUltraGT5;
    unsigned char ucTxDnlUltraGT6;
    unsigned char ucTxDnlUltraGT7;
} WIFI_NVRAM_TX_DNL_T, *P_WIFI_NVRAM_TX_DNL_T;

typedef struct _WIFI_NVRAM_LNA_GAIN_CAL_T{
    unsigned char ucRxCal1;
    unsigned char ucRxCal2;
    unsigned char ucRxCal3;
    unsigned char ucRxCal4;
    unsigned char ucRxCal5;
    unsigned char ucRxCal6;
} WIFI_NVRAM_LNA_GAIN_CAL_T, *P_WIFI_NVRAM_LNA_GAIN_CAL_T;

WLAN_STATUS wifiNVRAMTssiChOfsAdjust(unsigned int wf, ENUM_BAND_T eBand, unsigned int ch, unsigned int targetPwr, unsigned int MeanPwr);
int META_WIFI_init(void);
void META_WIFI_deinit(void);
void META_WIFI_OP(FT_WM_WIFI_REQ *req, char *peer_buf, unsigned short peer_len);
WLAN_STATUS wifiHqaGetDumpReCal(unsigned int item, unsigned int dbdcBandIdx, P_RECAL_INFO_T prReCalInfo);
WLAN_STATUS wifiHqaDoCalibrationTestItem(unsigned int item, unsigned int dbdcBandIdx);

#endif
