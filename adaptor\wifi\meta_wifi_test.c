#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include "meta_wifi.h"
#include <cutils/log.h>
#include <arpa/inet.h>
#include <unistd.h>

#ifdef LOG_TAG
#undef  LOG_TAG
#endif
#define LOG_TAG         "WIFI_META "

static void wifi_info_callback(FT_WM_WIFI_CNF *cnf, void *buf, unsigned int size)
{
    unsigned int i;
    char *type[] = { "WIFI_CMD_SET_OID", "WIFI_CMD_QUERY_OID", "WIFI_CMD_NVRAM_WRITE_ACCESS", "WIFI_CMD_NVRAM_READ_ACCESS"
                     , "WIFI_CMD_INIT", "WIFI_CMD_DEINIT", "WIFI_CMD_SCRIPT", "WIFI_CMD_HQA", "WIFI_CMD_PL_CALIBRATION"
                   };
    OID_STRUC *poid;

    P_CMD_PL_CAL prCmdPlCal;

    if (buf == NULL)
    {
        printf("[META_WIFI] %s is Null!\n", __func__);
        return;
    }

    printf("[META_WIFI] <CNF> %s, Drv Status: %d, Status: %d\n", type[cnf->type],
           cnf->drv_status, cnf->status);

    switch (cnf->type)
    {
        case WIFI_CMD_SET_OID:
        case WIFI_CMD_QUERY_OID:
            {
                poid = (OID_STRUC *)buf;
                printf("META_WIFI] <CNF> OID: %d, data len: %d\n",
                       poid->QueryOidPara.oid, poid->QueryOidPara.dataLen);

                for (i = 0; i < poid->QueryOidPara.dataLen; i++)
                {
                    printf("META_WIFI] <CNF> Data[%d] = 0x%x\n",
                           i, poid->QueryOidPara.data[i]);
                }

                break;
            }

        case WIFI_CMD_SCRIPT:
            {
                printf("[META_WIFI] <CNF> DataBuf(%d)= %s\n", size, buf);
                break;
            }

        case WIFI_CMD_PL_CALIBRATION:
            {
                prCmdPlCal = (P_CMD_PL_CAL)buf;
                printf("[META_WIFI] <WIFI_CMD_PL_CALIBRATION> id=%d,act=%d,Len=%d\n",
                       prCmdPlCal->calId,
                       prCmdPlCal->action,
                       prCmdPlCal->inputLen);

                for (i = 0; i < prCmdPlCal->inputLen; i++)
                {
                    printf("META_WIFI] Data[%d] = 0x%08x\n",
                           i, prCmdPlCal->au4Buffer[i]);
                }

                break;
            }

        default:
            {
                printf("[META_WIFI] %s is Null!\n", __func__);
                return;
            }
    }
}

void DoTestTssiChOffset()
{
    FT_WM_WIFI_REQ req;

    CMD_PL_CAL rCmdPlCal;
    P_HQA_CMD_FRAME prHqaCmd;
    HQA_SET_TX_PATH rSetTxPath;
    HQA_SET_TX_POWER rSetTxPower;
    HQA_SET_CH rSetCh;
    int u4hqaCmdLen = 0;
    int ext_id = 0;

    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(HQA_SET_CH);
    prHqaCmd = (P_HQA_CMD_FRAME)malloc(u4hqaCmdLen);

    memset(&req, 0, sizeof(FT_WM_WIFI_REQ));
    memset(prHqaCmd, 0, u4hqaCmdLen);


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_OPEN_ADAPTER); //OPEN ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 1;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================HQA OPEN ADAPTER DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_START;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL TXPWR -START DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPath);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 1;
    rSetTxPath.tx_path = htonl(0x01);
    rSetTxPath.band_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPath, sizeof(rSetTxPath));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPath);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA Set Tx Path DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPowerExt);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 2;
    rSetTxPower.power = htonl(0x1E);
    rSetTxPower.band_idx = htonl(0x00);
    rSetTxPower.channel = htonl(0x00);
    rSetTxPower.channel_band = htonl(0x00);
    rSetTxPower.ant_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPower, sizeof(rSetTxPower));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPower);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA Set Tx Power DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(7); //channel 7
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(0); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND); //OPEN ADAPTER
    prHqaCmd->length = htons(sizeof(unsigned int));
    prHqaCmd->sequence = 4;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    ext_id = htonl(HQA_CMD_DBDCStartTx); //HQA_DBDCSetChannel
    memcpy(&prHqaCmd->data[0], &ext_id, sizeof(ext_id));
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA START TX DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND); //OPEN ADAPTER
    prHqaCmd->length = htons(sizeof(unsigned int));
    prHqaCmd->sequence = 4;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    ext_id = htonl(HQA_CMD_DBDCStopTx); //HQA_DBDCSetChannel
    memcpy(&prHqaCmd->data[0], &ext_id, sizeof(ext_id));
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA STOP TX DONE====================\n");

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 1;
    rCmdPlCal.au4Buffer[0] = 0x1000; //Tool Measurement(S23.8)
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL ADJUST DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPath);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 1;
    rSetTxPath.tx_path = htonl(0x01);
    rSetTxPath.band_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPath, sizeof(rSetTxPath));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPath);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R2:HQA Set Tx Path DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPowerExt);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 2;
    rSetTxPower.power = htonl(0x16);
    rSetTxPower.band_idx = htonl(0x00);
    rSetTxPower.channel = htonl(0x00);
    rSetTxPower.channel_band = htonl(0x00);
    rSetTxPower.ant_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPower, sizeof(rSetTxPower));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPower);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R2:HQA Set Tx Power DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(6); //channel 6
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(0); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R2:HQA DBDC Set Channel DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND); //OPEN ADAPTER
    prHqaCmd->length = htons(sizeof(unsigned int));
    prHqaCmd->sequence = 4;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    ext_id = htonl(HQA_CMD_DBDCStartTx); //HQA_DBDCSetChannel
    memcpy(&prHqaCmd->data[0], &ext_id, sizeof(ext_id));
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R2:HQA START TX DONE====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND); //OPEN ADAPTER
    prHqaCmd->length = htons(sizeof(unsigned int));
    prHqaCmd->sequence = 4;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    ext_id = htonl(HQA_CMD_DBDCStopTx); //HQA_DBDCSetChannel
    memcpy(&prHqaCmd->data[0], &ext_id, sizeof(ext_id));
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R2:HQA STOP TX DONE====================\n");

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_END;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 1;
    rCmdPlCal.au4Buffer[0] = 0; /*success*/
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL TXPWR -FINISH DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_CLOSE_ADAPTER); //CLOSE  ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 2;
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================CLOSE ADAPTER Done====================\n");


#if 1
    int inputCh[] = {3, 0, 4, 1, 4, 7, 13};

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_INTERPOLAT;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = sizeof(inputCh) / sizeof(int);
    rCmdPlCal.au4Buffer[0] = 0; /*success*/
    memcpy(&rCmdPlCal.au4Buffer[0], &inputCh[0], sizeof(inputCh));
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL TXPWR - Interpolation ====================\n");
#endif

    FREEIF(prHqaCmd);

}
void DoTest2G4Interpolation()
{

    FT_WM_WIFI_REQ req;

    CMD_PL_CAL rCmdPlCal;
    memset(&rCmdPlCal, 0, sizeof(rCmdPlCal));

    int inputCh[] = {3, 0, 4, 1, 4, 7, 13};

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_INTERPOLAT;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = sizeof(inputCh) / sizeof(int);
    rCmdPlCal.au4Buffer[0] = 0; /*success*/
    memcpy(&rCmdPlCal.au4Buffer[0], &inputCh[0], sizeof(inputCh));
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL TXPWR - 2.4G Interpolation ====================\n");

}
void DoTest5GInterpolation()
{

    FT_WM_WIFI_REQ req;

    CMD_PL_CAL rCmdPlCal;
    memset(&rCmdPlCal, 0, sizeof(rCmdPlCal));

    int inputCh[] = {2, 0, 14, 36, 48, 52, 64, 100, 112, 116, 128, 132, 144, 149, 165, 184, 192};

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_TX_PWR;
    rCmdPlCal.action = TX_PWR_CAL_ACT_INTERPOLAT;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = sizeof(inputCh) / sizeof(int);
    rCmdPlCal.au4Buffer[0] = 0; /*success*/
    memcpy(&rCmdPlCal.au4Buffer[0], &inputCh[0], sizeof(inputCh));
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL TXPWR - 5G Interpolation ====================\n");
}
void DoTestDNLCal()
{

    FT_WM_WIFI_REQ req;
    P_HQA_CMD_FRAME prHqaCmd;
    HQA_SET_CH rSetCh;
    HQA_SET_TX_PATH rSetTxPath;
    CMD_PL_CAL rCmdPlCal;

    int u4hqaCmdLen = 0;

    printf("====================DoTestDNLCal====================\n");

    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(HQA_SET_CH);
    prHqaCmd = (P_HQA_CMD_FRAME)malloc(u4hqaCmdLen);


    memset(&req, 0, sizeof(FT_WM_WIFI_REQ));
    memset(prHqaCmd, 0, u4hqaCmdLen);


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_OPEN_ADAPTER); //OPEN ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 1;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================HQA OPEN ADAPTER DONE====================\n");

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_DNL_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_START;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL DNL -START DONE====================\n");


#if 1
    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPath);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 1;
    rSetTxPath.tx_path = htonl(0x01);
    rSetTxPath.band_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPath, sizeof(rSetTxPath));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPath);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA Set Tx Path DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(7); //channel 6
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(0); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_DNL_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================DNL adjust done!====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(192); //channel 8
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(1); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_DNL_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================DNL adjust done!====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(100); //channel 100
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(1); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_DNL_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================DNL adjust done!====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_DNL_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_END;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================DNL END done!====================\n");


#endif

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_CLOSE_ADAPTER); //CLOSE  ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 2;
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================CLOSE ADAPTER Done====================\n");


    FREEIF(prHqaCmd);

}
void DoTestLnaGainCal()
{

    FT_WM_WIFI_REQ req;
    P_HQA_CMD_FRAME prHqaCmd;
    HQA_SET_CH rSetCh;
    HQA_SET_TX_PATH rSetTxPath;
    CMD_PL_CAL rCmdPlCal;

    int u4hqaCmdLen = 0;

    printf("====================DoTestLnaGainCal====================\n");

    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(HQA_SET_CH);
    prHqaCmd = (P_HQA_CMD_FRAME)malloc(u4hqaCmdLen);


    memset(&req, 0, sizeof(FT_WM_WIFI_REQ));
    memset(prHqaCmd, 0, u4hqaCmdLen);


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_OPEN_ADAPTER); //OPEN ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 1;
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================HQA OPEN ADAPTER DONE====================\n");

    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_LNA_GAIN_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_START;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================PL LNA GAIN CAL -START DONE====================\n");


#if 1
    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_SetTxPath);
    prHqaCmd->length = htons(sizeof(rSetTxPath));
    prHqaCmd->sequence = 1;
    rSetTxPath.tx_path = htonl(0x01);
    rSetTxPath.band_idx = htonl(0x00);
    memcpy(&prHqaCmd->data[0], &rSetTxPath, sizeof(rSetTxPath));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetTxPath);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA Set Tx Path DONE====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(7); //channel 6
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(0); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_LNA_GAIN_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================LNA GAIN CAL adjust done!====================\n");


    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(192); //channel 8
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(1); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_LNA_GAIN_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================LNA GAIN CAL adjust done!====================\n");

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_EXTEND);
    prHqaCmd->length = htons(sizeof(rSetCh));
    prHqaCmd->sequence = 3;
    rSetCh.ext_id = htonl(HQA_CMD_DBDCSetChannel); //HQA_DBDCSetChannel
    rSetCh.num_param = htonl(0x06);
    rSetCh.band_idx = htonl(0x00);
    rSetCh.central_ch0 = htonl(100); //channel 100
    rSetCh.central_ch1 = htonl(0x00);
    rSetCh.sys_bw = htonl(0);
    rSetCh.perpkt_bw = htonl(0);
    rSetCh.ch_band = htonl(1); //0:2.4G,1:5G
    memcpy(&prHqaCmd->data[0], &rSetCh, sizeof(rSetCh));
    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(rSetCh);
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================R1:HQA DBDC Set Channel DONE====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_LNA_GAIN_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_ADJUST;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================LNA GAIN CAL adjust done!====================\n");


    req.type = WIFI_CMD_PL_CALIBRATION;
    rCmdPlCal.calId =  WIFI_PL_CAL_LNA_GAIN_CAL;
    rCmdPlCal.action = TX_PWR_CAL_ACT_END;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.inputLen = 0;
    META_WIFI_OP(&req, (char *)&rCmdPlCal, sizeof(rCmdPlCal));
    printf("====================LNA GAIN CAL END done!====================\n");


#endif

    req.type = WIFI_CMD_HQA;
    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_CLOSE_ADAPTER); //CLOSE  ADAPTER
    prHqaCmd->length = 0;
    prHqaCmd->sequence = 2;
    META_WIFI_OP(&req, (char *)prHqaCmd, u4hqaCmdLen);
    printf("====================CLOSE ADAPTER Done====================\n");


    FREEIF(prHqaCmd);

}


void doScript(void)
{
    FT_WM_WIFI_REQ req;

    memset(&req, 0, sizeof(FT_WM_WIFI_REQ));

    req.type = WIFI_CMD_SCRIPT;

    char *cmd2 = "adb shell iwpriv driver set_mcr 2011 2011";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd2, strlen(cmd2));
    META_WIFI_OP(&req, cmd2, strlen(cmd2));

    char *cmd3 = "adb shell iwpriv driver get_mcr 0x820F4020";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd3, strlen(cmd3));
    META_WIFI_OP(&req, cmd3, strlen(cmd3));


    char *cmd4 = "adb shell iwpriv driver set_mcr 0x820F4020 0x14141414";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd4, strlen(cmd4));
    META_WIFI_OP(&req, cmd4, strlen(cmd4));


    char *cmd5 = "adb shell iwpriv driver get_mcr 0x820F4020";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd5, strlen(cmd5));
    META_WIFI_OP(&req, cmd5, strlen(cmd5));

    //GET_NOISE
    char *cmd6 = "adb shell iwpriv driver get_noise";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd6, strlen(cmd6));
    META_WIFI_OP(&req, cmd6, strlen(cmd6));

    //GET_NOISE
    char *cmd7 = "adb shell iwpriv get_noise";
    printf("[META_WIFI]  WIFI_CMD_SCRIPT execute:%s sizeof(%lu)\n", cmd7, strlen(cmd7));
    META_WIFI_OP(&req, cmd7, strlen(cmd7));


}
int main()
{

    META_WIFI_Register(wifi_info_callback);

    if (META_WIFI_init() == false)
    {
        printf("WLAN init failed\n");
        return -1;
    }

    /*Test Case: TSSI DNL OFFSET CALIBRATION*/
    //DoTestDNLCal();

    /*Test Case: TSSI Channel offset compensation*/
    //DoTestTssiChOffset();

    /*Test Case: LNA GAIN CAL*/
    //DoTestLnaGainCal();

    DoTest2G4Interpolation();


    /*Test Case: Tssi 5G RF Group Interpolation*/
    DoTest5GInterpolation();

    printf("Sleep 1s\n");
    usleep(1 * 1000 * 1000);

    META_WIFI_deinit();
    META_WIFI_Register(NULL);

    return 0;
}

