#ifndef __META_WIFI__CH_GROUP_H__
#define __META_WIFI__CH_GROUP_H__

#define BAND_2G4_IDX 0
#define BAND_5G_IDX 1
#define BAND_6G_IDX 2
#define BAND_NUM 3

/** chanle group support max num */
#define CH_GROUP_SUPPORT_MAX_NUM        16
#define CH_SUB_GROUP_SUPPORT_MAX_NUM    16

/** common category channel group info */
#define NVRAM_COMMON_CATEGORY_G_BAND_CH_GROUP_NUM        14
#define NVRAM_COMMON_CATEGORY_A_BAND_CH_GROUP_NUM         8

/** wf path module channel group info (Tssi Ch offset) */
#define NVRAM_TSSI_CH_OFFSET_G_BAND_CH_GROUP_NUM         14
#define NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM         16
#define NVRAM_TSSI_CH_OFFSET_6G_BAND_CH_GROUP_NUM        15
#define NVRAM_TSSI_CH_OFFSET_A_BAND_RF_GROUP_NUM          8


/** channel group category item */
typedef enum _ENUM_CH_GROUP_ITEM
{
    CH_GROUP_ITEM_COMMON = 0x00,
    CH_GROUP_ITEM_TSSI_CH = 0x01,
    CH_GROUP_ITEM_NUM
} ENUM_CH_GROUP_ITEM, *P_ENUM_CH_GROUP_ITEM;

/** channel sub-group category item */
typedef enum _ENUM_CH_SUB_GROUP_ITEM
{
    CH_SUB_GROUP_TSSI_CH = 0,
    CH_SUB_GROUP_ITEM_NUM
} ENUM_CH_SUB_GROUP_ITEM, *P_ENUM_CH_SUB_GROUP_ITEM;


typedef enum _ENUM_CH_SUB_GROUP_2G4_ITEM
{
    CH_SUB_GROUP_2G4_LOW = 0,
    CH_SUB_GROUP_2G4_MID,
    CH_SUB_GROUP_2G4_HIGH,
    CH_SUB_GROUP_2G4_ITEM_NUM
} ENUM_CH_SUB_GROUP_2G4_ITEM, *P_ENUM_CH_SUB_GROUP_2G4_ITEM;

typedef enum _ENUM_CH_SUB_GROUP_5G_ITEM
{
    CH_SUB_GROUP_5G_LOW = 0,
    CH_SUB_GROUP_5G_HIGH,
    CH_SUB_GROUP_5G_ITEM_NUM
} ENUM_CH_SUB_GROUP_5G_ITEM, *P_ENUM_CH_SUB_GROUP_5G_ITEM;


/** channel group info structure */
typedef struct _CH_GROUP_CLASS
{
    ENUM_CH_GROUP_ITEM eGroupId;
    unsigned char u1ChGroupSupportNum;
    unsigned char u1ChGroupBoundary[CH_GROUP_SUPPORT_MAX_NUM];
} CH_GROUP_CLASS, *P_CH_GROUP_CLASS;

/** channel power offset group info structure */
typedef struct _CH_SUB_GROUP_CLASS
{
    ENUM_CH_SUB_GROUP_ITEM eGroupId;
    unsigned char u1ChSubGroupCategoryNum;
    unsigned char u1ChSubGroupNotOrderedNum;
    unsigned char u1ChSubGroupBoundary[CH_SUB_GROUP_SUPPORT_MAX_NUM];
} CH_SUB_GROUP_CLASS, *P_CH_SUB_GROUP_CLASS;


#endif

