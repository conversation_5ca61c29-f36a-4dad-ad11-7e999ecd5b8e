/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2008
*
*  BY OPENING THIS FILE, BUYER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
*  THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE")
*  RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO BUYER ON
*  AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
*  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
*  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
*  NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
*  SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
*  SUPPLIED WITH THE MEDIATEK SOFTWARE, AND BUYER AGREES TO LOOK ONLY TO SUCH
*  THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. MEDIATEK SHALL ALSO
*  NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE RELEASES MADE TO BUYER'S
*  SPECIFICATION OR TO CONFORM TO A PARTICULAR STANDARD OR OPEN FORUM.
*
*  BUYER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND CUMULATIVE
*  LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
*  AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
*  OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY BUYER TO
*  MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
*
*  THE TRANSACTION CONTEMPLATED HEREUNDER SHALL BE CONSTRUED IN ACCORDANCE
*  WITH THE LAWS OF THE STATE OF CALIFORNIA, USA, EXCLUDING ITS CONFLICT OF
*  LAWS PRINCIPLES.  ANY DISPUTES, CONTROVERSIES OR CLAIMS ARISING THEREOF AND
*  RELATED THERETO SHALL BE SETTLED BY ARBITRATION IN SAN FRANCISCO, CA, UNDER
*  THE RULES OF THE INTERNATIONAL CHAMBER OF COMMERCE (ICC).
*
*****************************************************************************/

#ifndef __META_DRMKEY_INSTALL_H_
#define __META_DRMKEY_INSTALL_H_

#include "MetaPub.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    FT_DRMKEY_INSTALL_SET = 0,
    FT_DRMKEY_INSTALL_QUERY,
    FT_DRMKEY_INSTALL_END
} FT_DRMKEY_INSTALL_OP;

#define KEY_BLK_CREATE	0x01
#define KEY_BLK_WRITE	0x02
#define KEY_BLK_EOF		0x04

// REQ
typedef struct
{
    unsigned int    file_size;
    unsigned char   stage;
} FT_DRMKEY_INSTALL_SET_REQ;

typedef struct
{
    unsigned int req;  // unused
} FT_DRMKEY_INSTALL_QUERY_REQ;

typedef union
{
    FT_DRMKEY_INSTALL_SET_REQ   set_req;
    FT_DRMKEY_INSTALL_QUERY_REQ query_req;
} FT_DRMKEY_INSTALL_CMD_U;

typedef struct {
    FT_H	                 header;  
    FT_DRMKEY_INSTALL_OP     op;
    FT_DRMKEY_INSTALL_CMD_U  cmd;
} FT_DRMKEY_INSTALL_REQ;


// Confirm
typedef struct {
    unsigned int result;
} DRMKEY_INSTALL_SET_CNF;

typedef struct{
	unsigned int  keycount;
	unsigned int  keytype[512];
} DRMKEY_INSTALL_QUERY_CNF;

typedef union {
    DRMKEY_INSTALL_SET_CNF        set_cnf;
	DRMKEY_INSTALL_QUERY_CNF      keyresult;
} FT_DRMKEY_INSTALL_CNF_U;

typedef enum {
    DRMKEY_INSTALL_OK = 0,
    DRMKEY_INSTALL_FAIL = -1,
    DRMKEY_INSTALL_VERIFY_FAIL = -2
} FT_DRMKEY_INSTALL_RESULT;

typedef struct {
    FT_H	                  header;
    FT_DRMKEY_INSTALL_OP      op;
    FT_DRMKEY_INSTALL_RESULT  status;
    FT_DRMKEY_INSTALL_CNF_U   result;
} FT_DRMKEY_INSTALL_CNF;

void META_DRMKEY_INSTALL_OP(FT_DRMKEY_INSTALL_REQ *req, char *peer_buff, unsigned short peer_len);

#ifdef __cplusplus
};
#endif

#endif

