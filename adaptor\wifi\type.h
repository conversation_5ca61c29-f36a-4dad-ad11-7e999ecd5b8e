/*******************************************************************************
** Copyright (c) 2005 MediaTek Inc.
**
** All rights reserved. Copying, compilation, modification, distribution
** or any other use whatsoever of this material is strictly prohibited
** except in accordance with a Software License Agreement with
** MediaTek Inc.
********************************************************************************
*/
#ifndef _TYPE_H
#define _TYPE_H


#ifndef NULL
#define NULL  0
#endif


#if !defined(TRUE)
#define TRUE true
#endif

#if !defined(FALSE)
#define FALSE false
#endif

#define IN
#define OUT

#define DLL_FUNC

#define BIT(n)                          ((unsigned int) 1 << (n))
#define BITS(m,n)                       (~(BIT(m)-1) & ((BIT(n) - 1) | BIT(n)))

/* Type definition for WLAN STATUS */
#define WLAN_STATUS                 unsigned int
#define P_WLAN_STATUS               unsigned int*

#ifndef RX_ANT_
#define RX_ANT_
typedef enum
{
    AGC_RX_ANT_SEL,
    MPDU_RX_ANT_SEL,
    FIXED_0,
    FIXED_1
} RX_ANT_SEL;
#endif

#endif
