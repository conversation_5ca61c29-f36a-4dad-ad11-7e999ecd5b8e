#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <time.h>
//#include <linux/mtgpio.h>
#include "meta_msensor.h"
#include <linux/sensors_io.h>
#include <log/log.h>
#include <string.h>
#include <HfManagerWrapper.h>
#include "HfSensorType.h"

#define MS_PREFIX   "[Meta] MS: "
#define MMSLOGD(fmt, arg ...) ALOGD(MS_PREFIX fmt, ##arg)
#define MMSLOGE(fmt, arg ...) ALOGE(MS_PREFIX fmt, ##arg)
#define UNIT_SEC_TO_NS 1.0e9
#define UNIT_MS_TO_NS 1000000
#define SAMPLE_RAWDATA_PERIOD_MS 20 * UNIT_MS_TO_NS
#define TIMEOUT_MS 1500 //PC will report error if cannot receive data unit 1500ms
#define TIMEOUT_SEC 1.5

static void *hf_manager = NULL;
static void *hf_looper = NULL;

bool Meta_MSensor_Open(void)
{
    hf_manager = HfManagerCreate();
    hf_looper = HfLooperCreate(HfManagerGetFd(hf_manager), 64);
    if (HfManagerFindSensor(hf_manager, SENSOR_TYPE_MAGNETIC_FIELD) < 0) {
        MMSLOGE("msensor open fail!\n");
        goto err_out;
    }
    return true;

err_out:
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return false;
}

int8_t ms_exec_read_raw()
{
    sensors_event_t data[32] = {0};
    int8_t ret = -1;
    struct timespec start_time, cur_time;
    float delta_time = 0.0f;

    memset(&start_time, 0, sizeof(struct timespec));
    memset(&cur_time, 0, sizeof(struct timespec));
    HfManagerEnableSensor(hf_manager, SENSOR_TYPE_MAGNETIC_FIELD, SAMPLE_RAWDATA_PERIOD_MS, 0);
    HfManagerEnableRawData(hf_manager, SENSOR_TYPE_MAGNETIC_FIELD);
    clock_gettime(CLOCK_REALTIME, &start_time);
    do {
        int err = HfLooperEventLooperTimeout(hf_looper, data, 32, TIMEOUT_MS);
        if (err <= 0) {
            MMSLOGE("eventLooper err:%d\n", err);
            ret = -1;
            goto exit;
        }

        for (int i = 0; i < err; ++i) {
            if (data[i].reserved0 == RAW_ACTION) {
                MMSLOGD("ms_exec_read_raw:%f, %f,%f\n", data[i].data[0],
                    data[i].data[1], data[i].data[2]);
                ret = 0;
                goto exit;
            }
        }
        clock_gettime(CLOCK_REALTIME, &cur_time);
        delta_time = (float)(cur_time.tv_nsec - start_time.tv_nsec) / UNIT_SEC_TO_NS
            + cur_time.tv_sec - start_time.tv_sec;
    } while(delta_time < TIMEOUT_SEC);

exit:
    HfManagerDisableRawData(hf_manager, SENSOR_TYPE_MAGNETIC_FIELD);
    HfManagerDisableSensor(hf_manager, SENSOR_TYPE_MAGNETIC_FIELD);
    return ret;
}

int Meta_MSensor_OP()
{
    int8_t err = -1;
    err = ms_exec_read_raw();
    return err;
}

bool Meta_MSensor_Close(void)
{
    HfLooperDestroy(hf_looper);
    HfManagerDestroy(hf_manager);
    return true;
}
