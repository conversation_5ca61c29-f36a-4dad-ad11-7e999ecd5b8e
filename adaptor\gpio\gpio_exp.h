#ifndef __GPIO_H
#define __GPIO_H

//------------------------------------------------------------------------------
typedef enum {
    HW_GPIO0=0,   HW_GPIO1,    HW_GPIO2,     HW_GPIO3, 
    HW_GPIO4,     HW_GPIO5,    HW_GPIO6,     HW_GPIO7, 
    HW_GPIO8,     HW_GPIO9,    HW_GPIO10,    HW_GPIO11, 
    HW_GPIO12,    HW_GPIO13,   HW_GPIO14,    HW_GPIO15,
    HW_GPIO16,    HW_GPIO17,   HW_GPIO18,    HW_GPIO19,
    HW_GPIO20,    HW_GPIO21,   HW_GPIO22,    HW_GPIO23,
    HW_GPIO24,    HW_GPIO25,   HW_GPIO26,    HW_GPIO27,
    HW_GPIO28,    HW_GPIO29,   HW_GPIO30,    HW_GPIO31,
    HW_GPIO32,    HW_GPIO33,   HW_GPIO34,    HW_GPIO35,
    HW_GPIO36,    HW_GPIO37,   HW_GPIO38,    HW_GPIO39,
    HW_GPIO40,    HW_GPIO41,   HW_GPIO42,    HW_GPIO43,
    HW_GPIO44,    HW_GPIO45,   HW_GPIO46,    HW_GPIO47,
    HW_GPIO48,    HW_GPIO49,   HW_GPIO50,    HW_GPIO51,
    HW_GPIO52,    HW_GPIO53,   HW_GPIO54,    HW_GPIO55,
    HW_GPIO56,    HW_GPIO57,   HW_GPIO58,    HW_GPIO59,
    HW_GPIO60,    HW_GPIO61,   HW_GPIO62,    HW_GPIO63,
    HW_GPIO64,    HW_GPIO65,   HW_GPIO66,    HW_GPIO67,
    HW_GPIO68,    HW_GPIO69,   HW_GPIO70,    HW_GPIO71,
    HW_GPIO72,    HW_GPIO73,   HW_GPIO74,    HW_GPIO75,
    HW_GPIO76,    HW_GPIO77,   HW_GPIO78,    HW_GPIO79,
    HW_GPIO80,    HW_GPIO81,   HW_GPIO82,    HW_GPIO83,
    HW_GPIO84,    HW_GPIO85,   HW_GPIO86,    HW_GPIO87,
    HW_GPIO88,    HW_GPIO89,   HW_GPIO90,    HW_GPIO91,
    HW_GPIO92,    HW_GPIO93,   HW_GPIO94,    HW_GPIO95,
    HW_GPIO96,    HW_GPIO97,   HW_GPIO98,    HW_GPIO99,
    HW_GPIO100,   HW_GPIO101,  HW_GPIO102,   HW_GPIO103,
    HW_GPIO104,   HW_GPIO105,  HW_GPIO106,   HW_GPIO107,
    HW_GPIO108,   HW_GPIO109,  HW_GPIO110,   HW_GPIO111,    
    HW_GPIO112,   HW_GPIO113,  HW_GPIO114,   HW_GPIO115,
    HW_GPIO116,   HW_GPIO117,  HW_GPIO118,   HW_GPIO119,
    HW_GPIO120,   HW_GPIO121,  HW_GPIO122,   HW_GPIO123,
    HW_GPIO124,   HW_GPIO125,  HW_GPIO126,   HW_GPIO127,
    HW_GPIO128,   HW_GPIO129,  HW_GPIO130,   HW_GPIO131,
    HW_GPIO132,   HW_GPIO133,  HW_GPIO134,   HW_GPIO135,
    HW_GPIO136,   HW_GPIO137,  HW_GPIO138,   HW_GPIO139,
    HW_GPIO140,   HW_GPIO141,  HW_GPIO142,   HW_GPIO143,
    HW_GPIO144,   HW_GPIO145,  HW_GPIO146,   HW_GPIO147,
    HW_GPIO148,   HW_GPIO149,  HW_GPIO150,   HW_GPIO151,
    HW_GPIO152,   HW_GPIO153,  HW_GPIO154,   HW_GPIO155,
    HW_GPIO156,   HW_GPIO157,  HW_GPIO158,   HW_GPIO159,
    HW_GPIO160,   HW_GPIO161,  HW_GPIO162,   HW_GPIO163,
    HW_GPIO164,   HW_GPIO165,  HW_GPIO166,   HW_GPIO167,
    HW_GPIO168,   HW_GPIO169,  HW_GPIO170,   HW_GPIO171,
    HW_GPIO172,   HW_GPIO173,  HW_GPIO174,   HW_GPIO175,
    HW_GPIO176,   HW_GPIO177,  HW_GPIO178,   HW_GPIO179,
    HW_GPIO180,   HW_GPIO181,  HW_GPIO182,   HW_GPIO183,
    HW_GPIO184,   HW_GPIO185,  HW_GPIO186,   HW_GPIO187,
    HW_GPIO188,   HW_GPIO189,  HW_GPIO190,   HW_GPIO191,
    HW_GPIO192,   HW_GPIO193,  HW_GPIO194,   HW_GPIO195,
    HW_GPIO196,   HW_GPIO197,  HW_GPIO198,   HW_GPIO199,
    HW_GPIO200,   HW_GPIO201,  HW_GPIO202,   HW_GPIO203,
    HW_GPIO204,   HW_GPIO205,  HW_GPIO206,   HW_GPIO207,
    HW_GPIO208,   HW_GPIO209,  HW_GPIO210,   HW_GPIO211,
    HW_GPIO212,
    HW_GPIO_MAX
} HW_GPIO;
//------------------------------------------------------------------------------


#endif

