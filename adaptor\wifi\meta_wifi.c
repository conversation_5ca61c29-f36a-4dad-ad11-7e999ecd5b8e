#include "meta_wifi.h"
#include "meta_wifi_ch_group.h"

#include <cutils/properties.h>
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

#include <net/if_arp.h>         /* For ARPHRD_ETHER */
#include <sys/socket.h>         /* For AF_INET & struct sockaddr */
#include <netinet/in.h>         /* For struct sockaddr_in */
#include <netinet/if_ether.h>
#include <linux/wireless.h>

#include "cutils/misc.h"
#include "iwlibstub.h"


#include "libnvram.h"

#include "type.h"
#include <dlfcn.h>

#include "meta_afc/meta_afc.h"
#include "meta_c0c1/meta_c0c1.h"
#include "meta_utils/meta_cal_para.h"

#ifndef WIFI_DRV_MOD_PATH
#define WIFI_DRV_MOD_PATH         "/system/lib/modules/wlan.ko"
#endif
#ifndef WIFI_DRV_MOD_NAME
#define WIFI_DRV_MOD_NAME         "wlan"
#endif
#ifndef WIFI_DRV_MOD_ARG
#define WIFI_DRV_MOD_ARG          ""
#endif
#ifndef WIFI_TYPE_NAME
#define WIFI_TYPE_NAME            "wlan"
#endif

#define WIFI_POWER_PATH     "/dev/wmtWifi"

static void *meta_data_handle = NULL;

#define VALUE(a) a?*a:0

#define AP_CFG_RDEB_FILE_WIFI_LID (VALUE((int *)dlsym(meta_data_handle, "iFileWIFILID")))
#define TEST_DATA_OK            (VALUE((int *)dlsym(meta_data_handle, "test_data_ok")))
#define WIFI_NVRAM_VERSION          (VALUE((int *)dlsym(meta_data_handle, "VAR_WIFI_NVRAM_VERSION")))
#define META_SUPPORT_PRODUCT_LINE_CAL   (VALUE((int *)dlsym(meta_data_handle, "VAR_META_SUPPORT_PRODUCT_LINE_CAL")))

#define NVRAM_G_BAND_TSSI_CH_GROUP_NUM     14
#define NVRAM_A_BAND_TSSI_CH_GROUP_NUM     16
#define NVRAM_6G_BAND_TSSI_CH_GROUP_NUM     15

#define MAX_NVRAM_ACCESS_SIZE           (VALUE((int *)dlsym(meta_data_handle, "MAX_NVRAM_ACCESS_SIZE")))
#define NVRAM_G_BAND_TSSI_CH_GROUP_NUM  (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_CH_GROUP_NUM")))
#define NVRAM_A_BAND_TSSI_CH_GROUP_NUM  (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_CH_GROUP_NUM")))
#define NVRAM_6G_BAND_TSSI_CH_GROUP_NUM  (VALUE((int *)dlsym(meta_data_handle, "NVRAM_6G_BAND_TSSI_CH_GROUP_NUM")))
#define NVRAM_G_BAND_TSSI_CH_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_CH_OFS_SIZE")))
#define NVRAM_A_BAND_TSSI_CH_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_CH_OFS_SIZE")))
#define NVRAM_6G_BAND_TSSI_CH_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_6G_BAND_TSSI_CH_OFS_SIZE")))

#define NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0")))
#define NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1")))
#define NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0")))
#define NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1")))
#define NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0")))
#define NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1")))

#define NVRAM_G_BAND_TSSI_DNL_GROUP_NUM     (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_DNL_GROUP_NUM")))
#define NVRAM_A_BAND_TSSI_DNL_GROUP_NUM     (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_DNL_GROUP_NUM")))
#define NVRAM_G_BAND_TSSI_DNL_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_DNL_OFS_SIZE")))
#define NVRAM_A_BAND_TSSI_DNL_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_DNL_OFS_SIZE")))
#define NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF0")))
#define NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF1")))
#define NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF0")))
#define NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF1")))

#define NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM     (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_LNA_GAIN_CAL_GROUP_NUM")))
#define NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM     (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_LNA_GAIN_CAL_GROUP_NUM")))
#define NVRAM_G_BAND_LNA_GAIN_CAL_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_LNA_GAIN_CAL_OFS_SIZE")))
#define NVRAM_A_BAND_LNA_GAIN_CAL_OFS_SIZE   (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_LNA_GAIN_CAL_OFS_SIZE")))
#define NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0")))
#define NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1")))
#define NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0")))
#define NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1")))

#define NVRAM_TSSI_STEP_OFSET    (VALUE((int *)dlsym(meta_data_handle, "NVRAM_TSSI_STEP_OFSET")))

#define TSSI_CH_OFFSET_TH_GT7 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT7")))
#define TSSI_CH_OFFSET_TH_GT6 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT6")))
#define TSSI_CH_OFFSET_TH_GT5 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT5")))
#define TSSI_CH_OFFSET_TH_GT4 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT4")))
#define TSSI_CH_OFFSET_TH_GT3 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT3")))
#define TSSI_CH_OFFSET_TH_GT2 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT2")))
#define TSSI_CH_OFFSET_TH_GT1 (VALUE((int *)dlsym(meta_data_handle, "VAR_TSSI_CH_OFFSET_TH_GT1")))

#define parChGroupTbl ((void *)dlsym(meta_data_handle, "var_arChGroupTbl"))
#define parSubGroupTbl ((void *)dlsym(meta_data_handle, "var_arSubGroupTbl"))

#define MAX_PL_INPUT_ARG_NUM 32
#define POLLING_RECAL_RETRY_CNT 10
#define QUERY_RECAL_RETRY_CNT 3

#define GET_A_BAND_RF_GROUP(group) (((group % 2) == 0) ? (group / 2) : ((group - 1) / 2))

#define WFMETA_PER_LINE_DUMP_CNT    16

//static const char DRIVER_MODULE_NAME[]  = WIFI_DRV_MOD_NAME;
//static const char DRIVER_MODULE_TAG[]   = WIFI_DRV_MOD_NAME " ";
//static const char DRIVER_MODULE_ARG[]   = WIFI_DRV_MOD_ARG;
//static const char DRIVER_MODULE_PATH[]  = WIFI_DRV_MOD_PATH;
//static const char MODULE_FILE[]         = "/proc/modules";

static int   wifi_init = 0;
static int   wifi_skfd = -1;
//static int   wifi_rfkill_id = -1;
//static char *wifi_rfkill_state_path = NULL;
static WIFI_CNF_CB cnf_cb = NULL;

//extern int init_module(void *, unsigned long, const char *);
//extern int delete_module(const char *, unsigned int);
extern int sched_yield(void);
extern int ifc_init();
extern int ifc_up(const char *name);
extern int ifc_down(const char *name);
extern void ifc_close();

static F_INFO  gNvInfo;

#if defined(SUPPORT_AFC_C0C1)
static F_INFO gGpsNv;
int gFile_gps;
float gTemperature[12];
float gFoeppm[12];
#endif

static HQA_PARA_INFO gHqaParaInfo;

#define CH_GROUP_CLASS_TBL_ITEM_NUM CH_GROUP_ITEM_NUM
#define CH_SUB_GROUP_CLASS_TBL_ITEM_NUM CH_SUB_GROUP_ITEM_NUM




int IS_WIFI_GEN_VER_SOC2_0()
{
    if ((WIFI_NVRAM_VERSION & 0xF000) == CONNAC_SOC2_0)
        return 1;
    else
        return 0;
            }

int IS_WIFI_GEN_VER_SOC3_0()
        {
    if ((WIFI_NVRAM_VERSION & 0xF000) == CONNAC_SOC3_0)
        return 1;
    else
        return 0;
            }

int IS_WIFI_GEN_VER_SOC7_0()
{
    if ((WIFI_NVRAM_VERSION & 0xF000) == CONNAC_SOC7_0)
        return 1;
    else
        return 0;
}

int IS_CFG_TSSI_CH_GT_SAME()
{
    if ((WIFI_NVRAM_VERSION & 0xF000) == CONNAC_SOC3_0)
        return 1;
    else
        return 0;
}

int IS_CFG_DNL_CAL()
{
    if ((WIFI_NVRAM_VERSION & 0xF000) >= SUPPORT_SOC3_0_DNL_VER)
        return 1;
    else
        return 0;
}

static void wifi_send_resp(FT_WM_WIFI_CNF *cnf, void *buf, unsigned int size)
{
    if (cnf_cb)
        cnf_cb(cnf, buf, size);
    else
        WriteDataToPC(cnf, sizeof(FT_WM_WIFI_CNF), buf, size);
}

int write_data_to_driver(char *data, size_t length)
{
    int sz;
    int fd = -1;
    int ret = -1;
    int retry_cont;

    if (!data || !length)
        return ret;

    for (retry_cont = 0; retry_cont < 5; retry_cont++)
    {
        DBG("open retry_cont = (%d)", retry_cont);
        fd = open(WIFI_POWER_PATH, O_WRONLY);

        if (fd < 0)
        {
            usleep(1000000);
        }
        else
            break;
    }

    if (fd < 0)
    {
        DBG("open(%s) for write failed: %s (%d)", WIFI_POWER_PATH,
            strerror(errno), errno);
        goto out;
    }

    for (retry_cont = 0; retry_cont < 5; retry_cont++)
    {
        DBG("write retry_cont = (%d)", retry_cont);
        sz = write(fd, data, length);

        if (sz < 0)
        {
#if 0
            DBG("write(%s) failed: %s (%d)", WIFI_POWER_PATH, strerror(errno),
                errno);
            goto out;
#endif

            usleep(1000000);
        }
        else
            break;
    }

    if (sz > 0)
        ret = 0;
    else
    {
        DBG("write(%s) failed: %s (%d)", WIFI_POWER_PATH, strerror(errno),
            errno);
    }

out:

    if (fd >= 0)
        close(fd);

    return ret;
}
/*----------------------------------------------------------------------------*/
/**
* @brief wifi nvram control: read nvram and wirte nvram
*
* @param[in] fgSet, (true:write, false:read)
*
* @return new command size
*/
/*----------------------------------------------------------------------------*/
int wifiNVRAMCtrl(bool fgSet, PNVRAM_ACCESS_STRUCT pnvram)
{

    int ret = -1;
    int iFD;
    int readByteLen = -1;

    if (pnvram == NULL)
    {

        return -1;
    }

    DBG("[META_WIFI] NVRAMCtrl fg(%d) len(%d)\n", fgSet, pnvram->dataLen);


    if (fgSet)
    {
        DBG("[META_WIFI] write offset:%d (0x%04X)\n", pnvram->dataOffset, pnvram->dataOffset);
        iFD = open(gNvInfo.cFileName, O_WRONLY | O_CREAT, S_IRUSR | S_IRGRP);

        if (iFD >= 0)
        {
            if (lseek(iFD, pnvram->dataOffset, SEEK_SET) < 0)
            {
                ERR("[META_WIFI] read fail!");
                close(iFD);
                return -1;
            }

            write(iFD, pnvram->data, pnvram->dataLen);
            close(iFD);

            /* invoke protect data file mechanism */
            if (NVM_ProtectDataFile(AP_CFG_RDEB_FILE_WIFI_LID, 1) != 1)
            {
                ERR("[META_WIFI] NVM_ProtectDataFile(): set failed\n");
                ret = -1;
            }
            else
            {
                // invoke auto backup mechanism
                NVM_AddBackupFileNum(AP_CFG_RDEB_FILE_WIFI_LID);
                ret = 0;
            }
        }
        else
        {
            ERR("[META_WIFI] open file :%s fail!, iFD=%d", gNvInfo.cFileName, iFD);
            return -1;
        }


    }
    else
    {
        /* post-check for read access */
        DBG("[META_WIFI] read offset:%d (0x%04X)\n", pnvram->dataOffset, pnvram->dataOffset);

        if (NVM_ProtectDataFile(AP_CFG_RDEB_FILE_WIFI_LID, 0) != 1)
        {
            ERR("[META_WIFI] NVM_ProtectDataFile(): get failed\n");
            return -1;
        }

        iFD = open(gNvInfo.cFileName, O_RDONLY | O_CREAT, S_IRUSR | S_IRGRP);

        if (iFD >= 0)
        {
            if (lseek(iFD, pnvram->dataOffset, SEEK_SET) < 0)
            {
                ERR("[META_WIFI] lseek fail!");
                close(iFD);
                return -1;
            }

            readByteLen = read(iFD, pnvram->data, pnvram->dataLen);

            if (readByteLen <= 0)
            {
                ERR("[META_WIFI] read fail! ,readByteLen :%d", readByteLen);
                close(iFD);
                return -1;
            }

            close(iFD);

            ret = readByteLen;
        }
        else
        {
            ERR("[META_WIFI] open file :%s fail!", gNvInfo.cFileName);
            return -1;
        }


    }

    return ret;
}

#if defined(SUPPORT_AFC_C0C1)
WLAN_STATUS CO_TMS_NVRAMCtrl(F_INFO *gNv, int file_lid, unsigned int offset, unsigned char value)
{
    int ret = -1;
    int iFD = -1;
    PNVRAM_ACCESS_STRUCT pnvram = NULL;
    pnvram = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + sizeof(unsigned char));

    if (!pnvram)
    {
        DBG("[META_CAL] Out of memory in allocating pnvram\n");
        return META_WIFI_STATUS_FAIL;
    }

    pnvram->dataLen = sizeof(unsigned char);
    pnvram->dataOffset = offset;
    memcpy(&pnvram->data[0], &value, sizeof(value));

    DBG("[META_CAL] Write offset:%d (0x%04X)\n", pnvram->dataOffset, pnvram->dataOffset);
    iFD = open(gNv->cFileName, O_WRONLY | O_CREAT, S_IRUSR | S_IRGRP);

    if (iFD >= 0)
    {
        if (lseek(iFD, pnvram->dataOffset, SEEK_SET) < 0)
        {
            ERR("[META_CAL] Fail to read nvram");
            close(iFD);
            FREEIF(pnvram);
            return META_WIFI_STATUS_FAIL;
        }

        write(iFD, pnvram->data, pnvram->dataLen);
        close(iFD);

        // Invoke protect data file mechanism
        if (NVM_ProtectDataFile(file_lid, 1) != 1)
        {
            ERR("[META_CAL] Fail to set NVM_ProtectDataFile()\n");
            ret = META_WIFI_STATUS_FAIL;
        }
        else
        {
            // Invoke auto backup mechanism
            NVM_AddBackupFileNum(file_lid);
            ret = META_WIFI_STATUS_SUCCESS;
        }
    }
    else
    {
        ERR("[META_CAL] Fail to open file[%s], iFD[%d]\n", gNv->cFileName, iFD);
        ret = META_WIFI_STATUS_FAIL;
    }

    FREEIF(pnvram);

    return ret;
}
#endif

void wifwNVRAMWriteDataToDriver(void)
{
    char *pCmdNvram;
    int ret = -1;
    PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
    int readLen = -1;
    int cmdLen = -1;
    char *pBuf = NULL;

    pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(gNvInfo.i4RecSize + sizeof(NVRAM_ACCESS_STRUCT));

    if (!pQueryNv)
    {
        DBG("out of memory in allocating pQueryNv\n");
        return ;
    }

    /*read NVRAM contant*/
    pQueryNv->dataLen = gNvInfo.i4RecSize;
    pQueryNv->dataOffset = 0; /*read NVRAM range[0~ dataLen]*/
    readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
    pBuf = (char *)&pQueryNv->data[0];


    /*Fill wmtWifi command buffer*/
    cmdLen = gNvInfo.i4RecSize + 12;
    pCmdNvram = (char *)malloc(cmdLen);

    if (!pCmdNvram)
    {
        DBG("out of memory in allocating acnvram\n");
        goto exit;
    }

    memset(pCmdNvram, 0, cmdLen);
    strncpy(pCmdNvram, "WR-BUF:NVRAM", 12);
    memcpy(pCmdNvram + 12, pBuf, gNvInfo.i4RecSize);

    /*write cmd to wifi driver*/
    ret = write_data_to_driver(pCmdNvram, cmdLen);

    DBG("[META_WIFI] write NVRAM[%d] to driver done! ret =%d \n", readLen, ret);

exit:
    FREEIF(pCmdNvram);
    FREEIF(pQueryNv);

}

/*
* Control Wi-Fi power by RFKILL interface is deprecated.
* Use character device to control instead.
*/
#if 0
static int wifi_init_rfkill(void)
{
    char path[128];
    char buf[32];
    int fd, id;
    ssize_t sz;

    for (id = 0; id < 10 ; id++)
    {
        snprintf(path, sizeof(path), "/sys/class/rfkill/rfkill%d/type", id);
        fd = open(path, O_RDONLY);

        if (fd < 0)
        {
            printf("open(%s) failed: %s (%d)\n", path, strerror(errno), errno);
            return -1;
        }

        sz = read(fd, &buf, sizeof(buf));
        close(fd);

        if (sz >= (ssize_t)strlen(WIFI_TYPE_NAME) &&
                memcmp(buf, WIFI_TYPE_NAME, strlen(WIFI_TYPE_NAME)) == 0)
        {
            wifi_rfkill_id = id;
            break;
        }
    }

    if (id == 10)
        return -1;

    asprintf(&wifi_rfkill_state_path, "/sys/class/rfkill/rfkill%d/state",
             wifi_rfkill_id);

    return 0;
}

static int wifi_check_power(void)
{
    int sz;
    int fd = -1;
    int ret = -1;
    char buf;
    char *path = wifi_rfkill_state_path;

    if ((wifi_rfkill_id == -1) && wifi_init_rfkill())
        goto out;

    fd = open(path, O_RDONLY);

    if (fd < 0)
    {
        printf("open(%s) failed: %s (%d)", path, strerror(errno),
               errno);
        goto out;
    }

    sz = read(fd, &buf, 1);

    if (sz != 1)
    {
        printf("read(%s) failed: %s (%d)", path, strerror(errno),
               errno);
        goto out;
    }

    switch (buf)
    {
        case '1':
            ret = 1;
            break;

        case '0':
            ret = 0;
            break;
    }

out:

    if (fd >= 0)
        close(fd);

    return ret;
}

static int wifi_set_power(int on)
{
    int sz;
    int fd = -1;
    int ret = -1;
    const char buf = (on ? '1' : '0');

    if (wifi_rfkill_id == -1)
    {
        if (wifi_init_rfkill()) goto out;
    }

    fd = open(wifi_rfkill_state_path, O_WRONLY);

    if (fd < 0)
    {
        printf("open(%s) for write failed: %s (%d)", wifi_rfkill_state_path,
               strerror(errno), errno);
        goto out;
    }

    sz = write(fd, &buf, 1);

    if (sz < 0)
    {
        printf("write(%s) failed: %s (%d)", wifi_rfkill_state_path, strerror(errno),
               errno);
        goto out;
    }

    ret = 0;

out:

    if (fd >= 0) close(fd);

    return ret;
}
#else


static int wifi_set_power(int on)
{
    int ret = -1;
    const char buf = (on ? '1' : '0');

    if (on)
        wifwNVRAMWriteDataToDriver();

    ret = write_data_to_driver((char *)&buf, sizeof(buf));

    return ret;
}
#endif

#if 0
static int wifi_insmod(const char *filename, const char *args)
{
    void *module;
    unsigned int size;
    int ret;

    module = load_file(filename, &size);

    if (!module)
        return -1;

    ret = init_module(module, size, args);

    free(module);

    return ret;
}

static int wifi_rmmod(const char *modname)
{
    int ret = -1;
    int maxtry = 10;

    while (maxtry-- > 0)
    {
        ret = delete_module(modname, O_NONBLOCK | O_EXCL);

        if (ret < 0 && errno == EAGAIN)
            usleep(500000);
        else
            break;
    }

    if (ret != 0)
        ERR("Unable to unload driver \"%s\": %s\n", modname, strerror(errno));

    return ret;
}

static int wifi_is_loaded(void)
{
    FILE *proc;
    char line[sizeof(DRIVER_MODULE_TAG) + 10];

    if ((proc = fopen(MODULE_FILE, "r")) == NULL)
    {
        ERR("Could not open %s: %s", MODULE_FILE, strerror(errno));
        return 0;
    }

    while ((fgets(line, sizeof(line), proc)) != NULL)
    {
        if (strncmp(line, DRIVER_MODULE_TAG, strlen(DRIVER_MODULE_TAG)) == 0)
        {
            fclose(proc);
            return 1;
        }
    }

    fclose(proc);
    return 0;
}
#endif

void removeSubstring(char *s, const char *toremove)
{
    while ((s = strstr(s, toremove)) != NULL)
        memmove(s, s + strlen(toremove), 1 + strlen(s + strlen(toremove)));

}
/*----------------------------------------------------------------------------*/
/**
* @brief remove Script's head command, inclue: adb, shell, iwpriv,wlan0,driver
*
* @param[in] pStr, input string
*
* @return new command size
*/
/*----------------------------------------------------------------------------*/

#define WIFI_REMOVE_HEAD_SIZE 5
int wifiScriptRemoveHead(char *pStr)
{
    int strIndex = 0;
    char *removeStrArray[WIFI_REMOVE_HEAD_SIZE] =
    {
        "adb ",
        "shell ",
        "iwpriv ",
        "wlan0 ",
        "driver ",
    };

    if (!pStr)
        return FALSE;

    for (strIndex = 0 ; strIndex < WIFI_REMOVE_HEAD_SIZE ; strIndex++)
    {
        if (strncmp(pStr, removeStrArray[strIndex], strlen(removeStrArray[strIndex])) == 0)
            removeSubstring(pStr, removeStrArray[strIndex]);
    }

    return strlen(pStr);
}

WLAN_STATUS wifiNVRAMWriteByte(unsigned int offset, unsigned char value)
{
    PNVRAM_ACCESS_STRUCT pSetNv = NULL;
    pSetNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + sizeof(unsigned char));
    if (!pSetNv)
    {
        DBG("out of memory in allocating pSetNv\n");
        return META_WIFI_STATUS_FAIL;
    }
    pSetNv->dataLen = sizeof(unsigned char);
    pSetNv->dataOffset = offset;
    memcpy(&pSetNv->data[0], &value, sizeof(value));
    wifiNVRAMCtrl(NVRAM_WRITE, pSetNv);
    FREEIF(pSetNv);

    return META_WIFI_STATUS_SUCCESS;
}
unsigned char wifiNVRAMGetChBand(
    ENUM_BAND_T eBand
)
{
    unsigned char u1ChBand = 0;

    if (eBand == BAND_2G4)
    {
        u1ChBand = BAND_2G4_IDX;
    }
    else if (eBand == BAND_6G)
    {
        u1ChBand = BAND_6G_IDX;
    }
    else
    {
        u1ChBand = BAND_5G_IDX;
    }

    return u1ChBand;
}

unsigned char wifiNVRAMGetSubChGroup(
    ENUM_BAND_T eBand,
    unsigned char u1CenterCh,
    unsigned char u1ChGroupIdx,
    ENUM_CH_SUB_GROUP_ITEM eChSubGroupItem
)
{
    unsigned char u1Idx = 0;
    unsigned char u1ChBand;
    unsigned char u1SubGroupIdx = 0;
    unsigned char u1GroupIdx = 0;
    unsigned char u1BoundaryNum = 0;
    P_CH_SUB_GROUP_CLASS prChSubGroup = NULL;
    CH_SUB_GROUP_CLASS arSubGroupTbl[CH_SUB_GROUP_ITEM_NUM][BAND_NUM] ={0};

    /* get channel band */
    u1ChBand = wifiNVRAMGetChBand(eBand);

    if(parSubGroupTbl != NULL)
        memcpy(&arSubGroupTbl[0][0], parSubGroupTbl,sizeof(CH_SUB_GROUP_CLASS)*CH_SUB_GROUP_ITEM_NUM*BAND_NUM);
    else
        DBG("wifiNVRAMGetSubChGroup:parSubGroupTbl is NULL\n");


    /* get pointer of channel power offset sub-group table entry */
    for (u1Idx = 0 ; u1Idx < CH_SUB_GROUP_ITEM_NUM ; u1Idx++)
    {
        if (arSubGroupTbl[u1Idx][u1ChBand].eGroupId == eChSubGroupItem)
        {
            prChSubGroup = &(arSubGroupTbl[u1Idx][u1ChBand]);
            break;
        }
    }

    if (prChSubGroup == NULL)
    {
        DBG("Can't find Channel Sub Group mapping Band:%d,item:%d\n", eBand, eChSubGroupItem);
        return META_WIFI_STATUS_INVALID_PARA;
    }


    /* get boundary number for each group */
    u1BoundaryNum = prChSubGroup->u1ChSubGroupCategoryNum - 1;

    /* search channel power offset sub-group index for specific channel */
    for (u1GroupIdx = (u1ChGroupIdx * u1BoundaryNum); u1GroupIdx < ((u1ChGroupIdx + 1) * u1BoundaryNum); u1GroupIdx++)
    {
        /* increment channel group index when exceed channel group boundary */
        if (u1CenterCh > prChSubGroup->u1ChSubGroupBoundary[u1GroupIdx])
            u1SubGroupIdx++;
    }

    /* special case (channel number not in order) handler for 5G band channel group 0 */
    if ((0 == u1ChGroupIdx) && (BAND_5G == eBand))
    {
        /* cyclic left shift sub-group index by Non-ordered subgroup number */
        u1SubGroupIdx += prChSubGroup->u1ChSubGroupNotOrderedNum;
        u1SubGroupIdx %= prChSubGroup->u1ChSubGroupCategoryNum;
    }

    return u1SubGroupIdx;
}
unsigned char wifiNVRAMGetChGroup(ENUM_BAND_T eBand, unsigned char u1CenterCh, ENUM_CH_GROUP_ITEM eChGroupItem)
{
    unsigned char u1Idx;
    unsigned char u1ChGroupIdx = 0;
    unsigned char u1GroupIdx = 0;
    P_CH_GROUP_CLASS prChGroup = NULL;
    unsigned char u1ChBand;
    CH_GROUP_CLASS arChGroupTbl[CH_GROUP_ITEM_NUM][BAND_NUM] ={0};

    /* get channel band */
    u1ChBand = wifiNVRAMGetChBand(eBand);

    if(parChGroupTbl != NULL)
        memcpy(&arChGroupTbl[0][0], parChGroupTbl,sizeof(CH_GROUP_CLASS)*CH_GROUP_ITEM_NUM*BAND_NUM);
    else
        DBG("wifiNVRAMGetChGroup:parChGroupTbl is NULL\n");


    /* search Channel group class table entry */
    for (u1Idx = 0 ; u1Idx < CH_GROUP_ITEM_NUM ; u1Idx++)
    {
        if (arChGroupTbl[u1Idx][u1ChBand].eGroupId == eChGroupItem)
        {
            prChGroup = &(arChGroupTbl[u1Idx][u1ChBand]);
            break;
        }
    }

    if (prChGroup == NULL)
    {
        DBG("Can't find Channel Group mapping Band:%d,item:%d\n", eBand, eChGroupItem);
        return META_WIFI_STATUS_INVALID_PARA;
    }

    /* search class index for specific channel */
    for (u1GroupIdx = 0; u1GroupIdx < prChGroup->u1ChGroupSupportNum; u1GroupIdx++)
    {
        /* increment channel group index when exceed channel group boundary */
        if (u1CenterCh > prChGroup->u1ChGroupBoundary[u1GroupIdx])
            u1ChGroupIdx++;
    }

    /* cyclic channel group process for high channel number in group 0 */
    if (prChGroup->u1ChGroupSupportNum != 1)
        u1ChGroupIdx %= prChGroup->u1ChGroupSupportNum;

    DBG("GetCh:%d ,ID:%d ,Group Idx:%d\n", u1CenterCh, eChGroupItem, u1ChGroupIdx);

    return u1ChGroupIdx;


}

/*Thrshold bound is defined */
unsigned char wifiNVRAMTssiChGainTableSelect(int pwr)
{
    unsigned char table_ofs = 0;

    if (pwr >= TSSI_CH_OFFSET_TH_GT7)
        table_ofs = NVRAM_GT7_OFFSET;
    else if ((TSSI_CH_OFFSET_TH_GT7 > pwr) && (pwr >= TSSI_CH_OFFSET_TH_GT6))
        table_ofs = NVRAM_GT6_OFFSET;
    else if ((TSSI_CH_OFFSET_TH_GT6 > pwr) && (pwr >= TSSI_CH_OFFSET_TH_GT5))
        table_ofs = NVRAM_GT5_OFFSET;
    else if ((TSSI_CH_OFFSET_TH_GT5 > pwr) && (pwr >= TSSI_CH_OFFSET_TH_GT4))
        table_ofs = NVRAM_GT4_OFFSET;
    else if ((TSSI_CH_OFFSET_TH_GT4 > pwr) && (pwr >= TSSI_CH_OFFSET_TH_GT3))
        table_ofs = NVRAM_GT3_OFFSET;
    else if ((TSSI_CH_OFFSET_TH_GT3 > pwr) && (pwr >= TSSI_CH_OFFSET_TH_GT2))
        table_ofs = NVRAM_GT2_OFFSET;
    else
        DBG("out of Gain Table thrshold :%d\n", pwr);

    /* Low Bound <= pwr < Upper Bound*/
    DBG("GT7[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT7, 127);
    DBG("GT6[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT6, TSSI_CH_OFFSET_TH_GT7);
    DBG("GT5[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT5, TSSI_CH_OFFSET_TH_GT6);
    DBG("GT4[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT4, TSSI_CH_OFFSET_TH_GT5);
    DBG("GT3[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT3, TSSI_CH_OFFSET_TH_GT4);
    DBG("GT2[%d ~ %d]\n", TSSI_CH_OFFSET_TH_GT2, TSSI_CH_OFFSET_TH_GT3);
    DBG("Pwr:%d is filled in GT%d\n", pwr, (table_ofs + 2));

    return table_ofs;
}


WLAN_STATUS wifiNVRAMTssiChGainTableAllTheSame(unsigned int nvOfs, unsigned char value)
{
    unsigned int gtOfsArray[TSSI_CH_OFS_GT_NUM] = {NVRAM_GT2_OFFSET, NVRAM_GT3_OFFSET,
                                                   NVRAM_GT4_OFFSET, NVRAM_GT5_OFFSET, NVRAM_GT6_OFFSET, NVRAM_GT7_OFFSET
                                                  };
    int idx = 0;

    unsigned int gtNvOfs = 0;

    for (idx = 0 ; idx < TSSI_CH_OFS_GT_NUM ; idx++)
    {
        gtNvOfs = nvOfs + gtOfsArray[idx];
        wifiNVRAMWriteByte(gtNvOfs, value);
        DBG("GT%d [0x%x]=0x%x\n", (idx + 2), gtNvOfs, value);
    }

    return META_WIFI_STATUS_SUCCESS;
}

unsigned int wifiNVRAMTssiChGetNVRAMOfs(unsigned int wf,
                                        ENUM_BAND_T eBand, unsigned int ch, unsigned int *pOfs)
{

    unsigned int u4NvramOffset = 0;

    unsigned char groupId = wifiNVRAMGetChGroup(eBand, ch, CH_GROUP_ITEM_TSSI_CH);
    unsigned char subGroupId = wifiNVRAMGetSubChGroup(eBand, ch, groupId, CH_SUB_GROUP_TSSI_CH);

    if (ch <= 0)
    {
        DBG("Tssi Ch error:%d\n", ch);
        return META_WIFI_STATUS_INVALID_PARA;
    }

    if (wf >= WF_NUM)
    {
        DBG("Tssi wf error:%d\n", wf);
        return META_WIFI_STATUS_INVALID_PARA;
    }
    if (IS_WIFI_GEN_VER_SOC3_0())
    {
        if (eBand == BAND_2G4)
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += sizeof(WIFI_NVRAM_TSSI_CH_OFS_T) * groupId;
        }
        else if (eBand == BAND_6G)
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += (groupId * sizeof(WIFI_NVRAM_TSSI_CH_OFS_T)) + subGroupId;
        }
        else
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += (groupId * sizeof(WIFI_NVRAM_TSSI_CH_OFS_T)) + subGroupId;
        }
    }
    if (IS_WIFI_GEN_VER_SOC2_0())
    {
        if (eBand == BAND_2G4)
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += sizeof(WIFI_NVRAM_TSSI_CH_OFS_T) * groupId;

        }
        else
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += (groupId * (CH_SUB_GROUP_5G_ITEM_NUM) + subGroupId) * sizeof(WIFI_NVRAM_TSSI_CH_OFS_T);
        }
    }
    if (IS_WIFI_GEN_VER_SOC7_0())
    {
        if (eBand == BAND_2G4)
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += groupId;

        }
        else if (eBand == BAND_6G)
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += (groupId * 2) + subGroupId; /*2= Low channel group + High channel group*/

        }
        else
        {
            u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
            u4NvramOffset += (groupId * 2) + subGroupId; /*2= Low channel group + High channel group*/
        }
    }
    *pOfs = u4NvramOffset;

    DBG("NvramVer:%d Band[%d]Ch[%d]Gt[%d]SubGt[%d]TssiNvramoffset[%d]\n",WIFI_NVRAM_VERSION, eBand, ch,groupId,subGroupId, *pOfs);


    return META_WIFI_STATUS_SUCCESS;

}
WLAN_STATUS wifiNVRAMTssiChOfsRfGroupTheSame(unsigned int wf, ENUM_BAND_T eBand,
        unsigned int ch, unsigned int chGroup, char nvCompVal)
{

    unsigned char rfGroup = 0, subGroup = 0;
    unsigned int u4NvramOffset = 0;
    unsigned int chInter = 0;

    if (eBand != BAND_5G)
    {
        DBG("only Support 5G Channel\n");
        return META_WIFI_STATUS_INVALID_PARA;
    }

    rfGroup = GET_A_BAND_RF_GROUP(chGroup);
    subGroup = wifiNVRAMGetSubChGroup(eBand, ch, chGroup, CH_SUB_GROUP_TSSI_CH);

    DBG("group the same: ch[%d],tssiChGroup=%d,subGroup=%d,RfGroup=%d,nvCompVal=0x%02X\n",
        ch, chGroup, subGroup, rfGroup, nvCompVal);

    /*update (n) ~ (n+3) Tssi ch group in one RF Group*/
    for (chInter = (rfGroup * PER_CH_GROUP_IN_RF_GROUP) ; chInter < ((rfGroup * PER_CH_GROUP_IN_RF_GROUP) + PER_CH_GROUP_IN_RF_GROUP); chInter++)
    {
        u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
        if(IS_WIFI_GEN_VER_SOC3_0())
            u4NvramOffset += (chInter * sizeof(WIFI_NVRAM_TSSI_CH_OFS_T));
        else
            u4NvramOffset += (chInter * sizeof(unsigned char));
        if (IS_CFG_TSSI_CH_GT_SAME()) {
            DBG("update TssiChGroup[%d][%c],NvOfs=0x%08X,NvVal=0x%02X\n",
                chInter / 2,
                ((chInter % 2) == 0) ? ('L') : ('H'),
                u4NvramOffset,
                nvCompVal);

            wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
        } else {
            if (IS_WIFI_GEN_VER_SOC3_0())
                u4NvramOffset += OFFSET_OF(WIFI_NVRAM_TSSI_CH_OFS_T, ucTssiChOfsGT7);
            wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
            DBG("update TssiChGroup[%d][%c] nvOfs=0x%X,nvCompVal=0x%02X\n",
                chInter / 2,
                ((chInter % 2) == 0) ? ('L') : ('H'),
                u4NvramOffset,
                nvCompVal);
        }
    }

    return META_WIFI_STATUS_SUCCESS;

}


/*clear tssi channel offset*/
WLAN_STATUS wifiNVRAMTssiChOfsClear(void)
{
    NVRAM_ACCESS_STRUCT raNvram[] =
    {
        /*2.4G WF0 Tssi Ch*/
        {NVRAM_G_BAND_TSSI_CH_OFS_SIZE, NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0, {0}},
        /*2.4G WF1 Tssi Ch*/
        {NVRAM_G_BAND_TSSI_CH_OFS_SIZE, NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1, {0}},
        /*5G WF0 Tssi Ch*/
        {NVRAM_A_BAND_TSSI_CH_OFS_SIZE, NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0, {0}},
        /*5G WF1 Tssi Ch*/
        {NVRAM_A_BAND_TSSI_CH_OFS_SIZE, NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1, {0}},
        /*6G WF0 Tssi Ch*/
        {NVRAM_6G_BAND_TSSI_CH_OFS_SIZE, NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF0, {0}},
        /*6G WF1 Tssi Ch*/
        {NVRAM_6G_BAND_TSSI_CH_OFS_SIZE, NVRAM_6G_BAND_TSSI_CH_OFS_OFSETOF_WF1, {0}},

    };
    PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
    char *pBuf = NULL;

#if 0
    int readLen = 0, j = 0;
#endif
    int i = 0;

    pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

    if (pQueryNv == NULL)
    {
        ERR("[META_WIFI] No memory nvram\n");
        goto error;
    }

    for (i = 0 ; i < (sizeof(raNvram) / sizeof(NVRAM_ACCESS_STRUCT)) ; i++)
    {
        if(raNvram[i].dataOffset == 0)
        {
            ERR("wifiNVRAMTssiChOfsClear :(%d) is offset 0!\n",i);
            continue;
        }

        pQueryNv->dataLen = raNvram[i].dataLen;
        pQueryNv->dataOffset = raNvram[i].dataOffset;
        pBuf = (char *)&pQueryNv->data[0];
#if 0
        /*read before clean*/
        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        DBG("[META_WIFI] 1st read len= %d\n", readLen);

        for (j = 0; j < readLen; j++)
            DBG("[META_WIFI] NVofs(0x%08X)[%p] = 0x%02X\n", (pQueryNv->dataOffset + j), (pBuf + j), *(pBuf + j));

#endif
        /*clear NVRAM content*/
        memset(pBuf, 0, MAX_NVRAM_ACCESS_SIZE);
        wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

#if 0
        /*read again*/
        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        DBG("[META_WIFI] 2st read len= %d\n", readLen);

        for (j = 0; j < readLen; j++)
            DBG("[META_WIFI] NVofs(0x%08X)[%p] = 0x%02X\n", (pQueryNv->dataOffset + j), (pBuf + j), *(pBuf + j));

#endif
    }

    DBG("Finish!\n");
error:
    FREEIF(pQueryNv);

    return META_WIFI_STATUS_SUCCESS;

}

WLAN_STATUS wifiNVRAMTssiContentDumpToPC(ENUM_BAND_T eBand, P_CMD_PL_CAL pCmdPLcal, WLAN_STATUS kResult)
{

    PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
    int readLen = 0, buffIdx = 0;

    pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

    if (!pQueryNv)
    {
        DBG("out of memory in allocating pQueryNv\n");
        return META_WIFI_STATUS_FAIL;
    }
    memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

    /*debug flow:add response data to PC*/
    pCmdPLcal->au4Buffer[buffIdx++] = kResult;

    if (eBand == BAND_2G4)
    {
        pCmdPLcal->au4Buffer[buffIdx++] = (NVRAM_G_BAND_TSSI_CH_OFS_SIZE << 16) | NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0;

        /*read 2.4G WF0 TSSI CH OFS*/
        pQueryNv->dataLen = NVRAM_G_BAND_TSSI_CH_OFS_SIZE;
        pQueryNv->dataOffset = NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF0;
        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        if (readLen < 0)
        {
            DBG("wifiNVRAMCtrl fail\n");
            kResult = META_WIFI_STATUS_FAIL;
            goto error;
        }
        memcpy(&pCmdPLcal->au4Buffer[buffIdx], &pQueryNv->data[0], readLen);
        buffIdx += readLen / sizeof(unsigned int);

        /*clear buffer*/
        memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        pCmdPLcal->au4Buffer[buffIdx++] = (NVRAM_G_BAND_TSSI_CH_OFS_SIZE << 16) | NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1;

        /*read 2.4G WF1 TSSI CH OFS*/
        pQueryNv->dataLen = NVRAM_G_BAND_TSSI_CH_OFS_SIZE;
        pQueryNv->dataOffset = NVRAM_G_BAND_TSSI_CH_OFS_OFSETOF_WF1;

        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        if (readLen < 0)
        {
            DBG("wifiNVRAMCtrl fail\n");
            kResult = META_WIFI_STATUS_FAIL;
            goto error;
        }
        memcpy(&pCmdPLcal->au4Buffer[buffIdx], &pQueryNv->data[0], readLen);
        buffIdx += readLen / sizeof(unsigned int);
        memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        /*update data length*/
        pCmdPLcal->inputLen = buffIdx;

    }
    else
    {
        pCmdPLcal->au4Buffer[buffIdx++] = (NVRAM_A_BAND_TSSI_CH_OFS_SIZE << 16) | NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0;

        /*read 5G WF0 TSSI CH OFS*/
        pQueryNv->dataLen = NVRAM_A_BAND_TSSI_CH_OFS_SIZE;
        pQueryNv->dataOffset = NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0;
        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        if (readLen < 0)
        {
            DBG("wifiNVRAMCtrl fail\n");
            kResult = META_WIFI_STATUS_FAIL;
            goto error;
        }
        memcpy(&pCmdPLcal->au4Buffer[buffIdx], &pQueryNv->data[0], readLen);
        buffIdx += readLen / sizeof(unsigned int);
        memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        pCmdPLcal->au4Buffer[buffIdx++] = (NVRAM_A_BAND_TSSI_CH_OFS_SIZE << 16) | NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1;

        /*read 5G WF1 TSSI CH OFS*/
        pQueryNv->dataLen = NVRAM_A_BAND_TSSI_CH_OFS_SIZE;
        pQueryNv->dataOffset = NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1;

        readLen = wifiNVRAMCtrl(NVRAM_READ, pQueryNv);
        if (readLen < 0)
        {
            DBG("wifiNVRAMCtrl fail\n");
            kResult = META_WIFI_STATUS_FAIL;
            goto error;
        }
        memcpy(&pCmdPLcal->au4Buffer[buffIdx], &pQueryNv->data[0], readLen);
        buffIdx += readLen / sizeof(unsigned int);
        memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        /*update data length*/
        pCmdPLcal->inputLen = buffIdx;

    }
error:
    FREEIF(pQueryNv);

    DBG("Band[%d] Calibration Result:%d dump done!\n", eBand, kResult);

    return kResult;
}

unsigned int wifiNVRAMCalculateShift()
{
    unsigned int ret = 7;
    unsigned char tssiStep;
    unsigned int readLine = 0;
    PNVRAM_ACCESS_STRUCT pSetNv = NULL;

    /* read from NVram calculateShift */
    pSetNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + sizeof(unsigned char));

    if (!pSetNv)
    {
        DBG("out of memory in allocating pSetNv\n");
        return ret;
    }

    pSetNv->dataLen = sizeof(unsigned char);
    pSetNv->dataOffset = NVRAM_TSSI_STEP_OFSET;
    readLine = wifiNVRAMCtrl(NVRAM_READ, pSetNv);
    memcpy(&tssiStep, &pSetNv->data[0], sizeof(unsigned char));

    DBG("calculate Shift tssiStep=[0x%X] readLen=%d\n", tssiStep, readLine);

    switch (tssiStep)
    {
        case 0x83:
            /* precision 0.125 translate q-format from .8 to .3 */
            ret = 5;
            break;

        case 0x82:
            /* precision 0.25 translate q-format from .8 to .2 */
            ret = 6;
            break;
        case 0x81:
        case 0x80:
        default:
            /* precision 0.5 translate q-format from .8 to .1 */
            ret = 7;
    }

    FREEIF(pSetNv);
    return ret;

}

WLAN_STATUS wifiNVRAMTssiChOfsAdjust(unsigned int wf, ENUM_BAND_T eBand, unsigned int ch, unsigned int targetPwr, unsigned int MeanPwr)
{

    unsigned int u4NvramOffset = 0;
    unsigned char GTofs = 0;
    char tssiChofsValue = 0;
    unsigned int shift = 5;

    if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, ch, &u4NvramOffset) != META_WIFI_STATUS_SUCCESS)
    {
        DBG("Get Ch offset fial!\n");
        return META_WIFI_STATUS_FAIL;
    }

    if (IS_WIFI_GEN_VER_SOC3_0())
    {
        /*decided to gain table compensation offset*/
        GTofs = wifiNVRAMTssiChGainTableSelect(targetPwr);
    }
    if (IS_WIFI_GEN_VER_SOC2_0())
    {
        shift = wifiNVRAMCalculateShift();
    }

    /*decided to tssi channel offset compensation*/
    /*TODO*/
    /*targetpwr = 8 bit format ,2's complement*/
    /*MeanPwr = s23.8 format*/
    /*tssiChofsValue format = s4.3,nvram use 2's complement firmware will covert 20 s4.3*/

    tssiChofsValue = (((targetPwr << 7) - MeanPwr) >> shift) & 0xFF;
    DBG("TargetPwr:0x%08X (%6.1fdBm), MeaPwr:0x%08X(%6.8fdBm),tssiChofsValue=0x%02X\n",
        targetPwr,
        ((float)targetPwr) / (2.0),
        MeanPwr,
        ((float)MeanPwr / (256.0)),
        tssiChofsValue);


    if (IS_CFG_TSSI_CH_GT_SAME()) {
        DBG("Tssi ch wf[%d]Band[%d]Ch[%d] GT the same\n"
            , wf
            , eBand
            , ch);
        wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, tssiChofsValue);
    } else {
        u4NvramOffset += GTofs;

        wifiNVRAMWriteByte(u4NvramOffset, tssiChofsValue);

        DBG("Tssi ch wf[%d]Band[%d]Ch[%d]GT[%d] ofs:0x%08X = 0x%x\n"
            , wf
            , eBand
            , ch
            , (GTofs + 2)
            , u4NvramOffset
            , tssiChofsValue);
    }

    return META_WIFI_STATUS_SUCCESS;
}


WLAN_STATUS wifiNVRAMTssiChOfsInterpolation_soc30(TX_PWR_INTERPOLATION_TYPE type,
        ENUM_BAND_T eBand, unsigned int chNum, unsigned int *pchArray)
{

    int wf = WF0;
    unsigned int u4NvramOffset;
    unsigned int chIdx = 0, rfIdx = 0;
    int ch0 = 0, ch1 = 0, chInter = 0;
    unsigned int readLine = 0;
    int devStep = 0;
    char nvCompVal = 0;
    WIFI_NVRAM_TSSI_CH_OFS_T rNvVal[NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2];
    PNVRAM_ACCESS_STRUCT pSetNv = NULL;
    int chOfs0 = 0, chOfs1 = 0;
    unsigned char chGroup[NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2];
    unsigned char rfGroup = 0;
    INTER_ACT interAct = INTER_ACT_NOT_SUPPORT;
    INTERPOLATION_CH_BOUND_A_BAND rfInterList[NVRAM_TSSI_CH_OFFSET_A_BAND_RF_GROUP_NUM] =
    {
        {0xFF, 0}, //RF Group 0, not support 5G Interpolation
        {36, 48}, //RF Group 1
        {52, 64}, //RF Group 2
        {0xFF, 0}, //RF Group 3, not support 5G Interpolation
        {100, 112}, //RF Group 4
        {116, 128}, //RF Group 5
        {132, 144}, //RF Group 6
        {149, 165}, //RF Group 7
    };



    /*init */
    pSetNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + sizeof(WIFI_NVRAM_TSSI_CH_OFS_T));
    if (!pSetNv)
    {
        DBG("out of memory in allocating pSetNv\n");
        return META_WIFI_STATUS_FAIL;
    }
    DBG("Band[%d] chNum[%d] Type[%d] Enter\n", eBand, chNum, type);
    memset(&rNvVal[0], 0, sizeof(WIFI_NVRAM_TSSI_CH_OFS_T) * (NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2));
    memset(&chGroup[0], 0, sizeof(unsigned char) * (NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2));

    for (wf = WF0; wf < WF_NUM; wf++)
    {

        //read GT table by channel range
        for (chIdx = 0; chIdx < chNum; chIdx++)
        {
            ch0 = pchArray[chIdx];
            chGroup[chIdx] = wifiNVRAMGetChGroup(eBand, ch0, CH_GROUP_ITEM_TSSI_CH);

            if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, ch0, &u4NvramOffset) != META_WIFI_STATUS_SUCCESS)
            {
                DBG("Get Ch offset fial!\n");
                FREEIF(pSetNv);
                return META_WIFI_STATUS_FAIL;
            }

            pSetNv->dataLen = sizeof(WIFI_NVRAM_TSSI_CH_OFS_T);
            pSetNv->dataOffset = u4NvramOffset;
            readLine = wifiNVRAMCtrl(NVRAM_READ, pSetNv);
            memcpy(&rNvVal[chIdx], &pSetNv->data[0], sizeof(WIFI_NVRAM_TSSI_CH_OFS_T));
            DBG("Read WF[%d]chIdx[%d] = %d,Group=%d,GT=[0x%X,0x%X,0x%X,0x%X,0x%X,0x%X] readLen=%d\n",
                wf,
                chIdx,
                ch0,
                chGroup[chIdx],
                rNvVal[chIdx].ucTssiChOfsGT2,
                rNvVal[chIdx].ucTssiChOfsGT3,
                rNvVal[chIdx].ucTssiChOfsGT4,
                rNvVal[chIdx].ucTssiChOfsGT5,
                rNvVal[chIdx].ucTssiChOfsGT6,
                rNvVal[chIdx].ucTssiChOfsGT7,
                readLine);
        }



        /*only 2.4G support interpolation -- start*/
        if ((type == TYPE_INTERPOLATION) && (eBand == BAND_2G4))
        {
            //calcaute each step ch=0 ~ pchArray(chNum-1)
            for (chIdx = 0; chIdx < chNum - 1; chIdx++)
            {

                ch0 = pchArray[chIdx];
                chOfs0 =  SIGNED_CONVERT_EXTEND_BITS(rNvVal[chIdx].ucTssiChOfsGT7, 8);


                ch1 = pchArray[(chIdx + 1)];
                chOfs1 = SIGNED_CONVERT_EXTEND_BITS(rNvVal[(chIdx + 1)].ucTssiChOfsGT7, 8);

                for (chInter = ch0 + 1 ; chInter < ch1; chInter++)
                {
                    u4NvramOffset = 0;

                    if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, chInter, &u4NvramOffset)
                            != META_WIFI_STATUS_SUCCESS)
                    {
                        DBG("Get Ch offset fial!\n");
                        FREEIF(pSetNv);
                        return META_WIFI_STATUS_FAIL;
                    }

                    devStep = ((chOfs1 - chOfs0) * (chInter - ch0)) / (ch1 - ch0);

                    DBG("Interpolation ch[%d ~ %d] tssiChVal=[%d ~ %d],ch[%d] + devStep=%d\n",
                        ch0, ch1, chOfs0, chOfs1, chInter, devStep);

                    nvCompVal = (chOfs0 + devStep) & 0x000000FF;

                    if (IS_CFG_TSSI_CH_GT_SAME()) {
                        wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                    } else {
                        u4NvramOffset += OFFSET_OF(WIFI_NVRAM_TSSI_CH_OFS_T, ucTssiChOfsGT7);
                        wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
                        DBG("ch[%d] nvOfs=0x%X,nvCompVal=0x%02X\n",
                            chInter, u4NvramOffset, nvCompVal);
                    }
                }
            }

            //calcaute each step pchArray(n+1) ~ ch=14
            for (chInter = ch1 + 1 ; chInter <= 14; chInter++)
            {
                u4NvramOffset = 0;

                if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, chInter, &u4NvramOffset) != META_WIFI_STATUS_SUCCESS)
                {
                    DBG("Get Ch offset fial!\n");
                    FREEIF(pSetNv);
                    return META_WIFI_STATUS_FAIL;
                }

                devStep = ((chOfs1 - chOfs0) * (chInter - ch1)) / (ch1 - ch0);

                DBG("Extrapolation ch[%d ~ %d] tssiChVal=[%d ~ %d],ch[%d] + devStep=%d\n",
                    ch1 + 1, 14, chOfs0, chOfs1, chInter, devStep);
                nvCompVal = chOfs0 + (chInter - ch0) * devStep;

                if (IS_CFG_TSSI_CH_GT_SAME()) {
                    wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                } else {
                    u4NvramOffset += OFFSET_OF(WIFI_NVRAM_TSSI_CH_OFS_T, ucTssiChOfsGT7);
                    wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
                    DBG("ch[%d] nvOfs=0x%X,nvCompVal=0x%02X\n",
                        chInter, u4NvramOffset, nvCompVal);
                }
            }
        }
        else if ((type == TYPE_GROUP_THE_SAME) && (eBand == BAND_5G))
        {
            /* "group the same" means using meanuse tx power value to apply tssi ch ofs group
                         *   in RF defined group
                         */
            //calcaute each step ch=0 ~ pchArray(chNum)
            for (chIdx = 0; chIdx < chNum ; chIdx++)
            {
                nvCompVal = rNvVal[chIdx].ucTssiChOfsGT7;
                wifiNVRAMTssiChOfsRfGroupTheSame(wf, eBand, pchArray[chIdx], chGroup[chIdx], nvCompVal);
            }
        }
        else if ((type == TYPE_INTERPOLATION) && (eBand == BAND_5G))
        {
            //calcaute each step ch=0 ~ pchArray(chNum-1)
            for (chIdx = 0; chIdx < chNum; chIdx++)
            {
                interAct = INTER_ACT_NOT_SUPPORT;

                //Check 5G interpolation support RF group channel bound
                for (rfIdx = 0; rfIdx < NVRAM_TSSI_CH_OFFSET_A_BAND_RF_GROUP_NUM; rfIdx++)
                {
                    DBG("ch=%d Check RFGroup[%d] bound[%d ~ %d]\n",
                        pchArray[chIdx],
                        rfIdx,
                        rfInterList[rfIdx].lowBoundCh,
                        rfInterList[rfIdx].upperBoundCh);

                    //if chIdx = low bound  and chIdx+1 = upper bound
                    if (pchArray[chIdx] == rfInterList[rfIdx].lowBoundCh)
                    {
                        if ((chIdx < (chNum - 1)) &&
                                pchArray[chIdx + 1] == rfInterList[rfIdx].upperBoundCh)
                        {
                            interAct = INTER_ACT_INTERPOLATION;

                            rfGroup = GET_A_BAND_RF_GROUP(chGroup[chIdx]);

                            ch0 = pchArray[chIdx];
                            ch1 = pchArray[(chIdx + 1)];

                            chOfs0 =  SIGNED_CONVERT_EXTEND_BITS(rNvVal[chIdx].ucTssiChOfsGT7, 8);
                            chOfs1 = SIGNED_CONVERT_EXTEND_BITS(rNvVal[(chIdx + 1)].ucTssiChOfsGT7, 8);

                            DBG("5G do Interpolation WF[%d] RF[%d] ch[%d ~ %d] Rang[%d ~ %d]\n",
                                wf,
                                rfGroup,
                                ch0,
                                ch1,
                                chOfs0,
                                chOfs1);

                            //Query the next RF group
                            chIdx++;
                            break;
                        }
                        else
                        {
                            interAct = INTER_ACT_GROUP_THE_SAME;
                            break;
                        }
                    }
                    else if ((pchArray[chIdx] > rfInterList[rfIdx].lowBoundCh) &&
                             (pchArray[chIdx] < rfInterList[rfIdx].upperBoundCh))
                    {
                        interAct = INTER_ACT_GROUP_THE_SAME;
                        break;
                    }
                    else
                        interAct = INTER_ACT_GROUP_THE_SAME;

                }

                if (interAct == INTER_ACT_GROUP_THE_SAME)
                {
                    nvCompVal = rNvVal[chIdx].ucTssiChOfsGT7;
                    wifiNVRAMTssiChOfsRfGroupTheSame(wf, eBand, pchArray[chIdx], chGroup[chIdx], nvCompVal);
                }
                else if (interAct == INTER_ACT_INTERPOLATION)
                {

                    /*update (n) ~ (n+3) Tssi ch group in one RF Group*/
                    for (chInter = (rfGroup * PER_CH_GROUP_IN_RF_GROUP) ; chInter < ((rfGroup * PER_CH_GROUP_IN_RF_GROUP) + PER_CH_GROUP_IN_RF_GROUP); chInter++)
                    {
                        u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
                        u4NvramOffset += (chInter * sizeof(WIFI_NVRAM_TSSI_CH_OFS_T));

                        devStep = ((chOfs1 - chOfs0) * (chInter - (rfGroup * PER_CH_GROUP_IN_RF_GROUP))) / (PER_CH_GROUP_IN_RF_GROUP - 1);

                        nvCompVal = (chOfs0 + devStep) & 0x000000FF;

                        DBG("Interpolation:TssiChGroup[%d][%c] + devStep=%d,NvOfs=0x%08X,NvVal=0x%02X\n",
                            chInter / 2,
                            ((chInter % 2) == 0) ? ('L') : ('H'),
                            devStep,
                            u4NvramOffset,
                            nvCompVal);

                        if (IS_CFG_TSSI_CH_GT_SAME()) {
                            wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                        } else {
                            u4NvramOffset += OFFSET_OF(WIFI_NVRAM_TSSI_CH_OFS_T, ucTssiChOfsGT7);

                            wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
                        }

                    }

                }
                else
                    DBG("Not Support ch[%d]!\n", pchArray[chIdx + 1]);

            }
        }
    }

    FREEIF(pSetNv);

    return META_WIFI_STATUS_SUCCESS;
}


WLAN_STATUS wifiNVRAMTssiNDLOfsClear(void)
{
    if (!IS_WIFI_GEN_VER_SOC3_0())
        return META_WIFI_STATUS_FAIL;
    if (IS_CFG_DNL_CAL()) {
        NVRAM_ACCESS_STRUCT raNvram[] =
        {
            /*2.4G WF0 Tssi DNL*/
            {NVRAM_G_BAND_TSSI_DNL_OFS_SIZE, NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF0, {0}},
            /*2.4G WF1 Tssi DNL*/
            {NVRAM_G_BAND_TSSI_DNL_OFS_SIZE, NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF1, {0}},
            /*5G WF0 Tssi DNL*/
            {NVRAM_A_BAND_TSSI_DNL_OFS_SIZE, NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF0, {0}},
            /*5G WF1 Tssi DNL*/
            {NVRAM_A_BAND_TSSI_DNL_OFS_SIZE, NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF1, {0}},

        };
        PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
        char *pBuf = NULL;
        int i = 0;

        pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        if (pQueryNv == NULL)
        {
            ERR("[META_WIFI] No memory nvram\n");
            goto error;
        }

        for (i = 0 ; i < (sizeof(raNvram) / sizeof(NVRAM_ACCESS_STRUCT)) ; i++)
        {
            pQueryNv->dataLen = raNvram[i].dataLen;
            pQueryNv->dataOffset = raNvram[i].dataOffset;
            pBuf = (char *)&pQueryNv->data[0];

            /*clear NVRAM content*/
            memset(pBuf, 0, MAX_NVRAM_ACCESS_SIZE);
            wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

        }

        DBG("Finish!\n");
    error:
        FREEIF(pQueryNv);
    }
    return META_WIFI_STATUS_SUCCESS;

}

WLAN_STATUS wifiNVRAMTssiDnlOfsAdjust(unsigned int dbdcbandIdx,
                                      ENUM_BAND_T eBand, unsigned char inputCh)
{
    WLAN_STATUS ret = META_WIFI_STATUS_FAIL;

    if (!IS_WIFI_GEN_VER_SOC3_0())
        return META_WIFI_STATUS_FAIL;

    if (IS_CFG_DNL_CAL()) {
        unsigned char chGroup = 0;
        unsigned int data_cnt = 0;
        char crOfsetIdx = 0;
        char chIdx = 0;
        int i = 0;
        RECAL_INFO_T rReCalInfo;
        WIFI_NVRAM_TX_DNL_T rDnl;

        unsigned int nvramOfs = 0;
        PNVRAM_ACCESS_STRUCT pQueryNv = NULL;

        pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);
        memset(&rReCalInfo, 0, sizeof(RECAL_INFO_T));

        //get ch group
        chGroup = wifiNVRAMGetChGroup(eBand, inputCh, CH_GROUP_ITEM_COMMON);

        DBG("DbdcIdx=%d,Band=%d,ch=%d,Group=%d\n", dbdcbandIdx, eBand, inputCh, chGroup);

        for (i = 0 ; i < QUERY_RECAL_RETRY_CNT ; i++)
        {

            //set NDL CAL by HQA CMD
            if (wifiHqaDoCalibrationTestItem(HQA_CAL_ITEM_DNL, dbdcbandIdx) != META_WIFI_STATUS_SUCCESS)
                DBG("Cal test item fail!\n");


            //query NDL CAL RESULT
            if (wifiHqaGetDumpReCal(HQA_CAL_ITEM_DNL, dbdcbandIdx, &rReCalInfo) != META_WIFI_STATUS_SUCCESS)
                DBG("Cal query item fail!\n");


            data_cnt = rReCalInfo.u4Count;

            if (data_cnt != (DNL_WF_PATH_CR_NUM * WF_NUM))
            {
                DBG("Result Num dismatch!,Expect:%d Total:%d do-try(%d)!\n",
                    (DNL_WF_PATH_CR_NUM * WF_NUM),
                    data_cnt, i);
                continue;
            }
            else
                break;
        }

        if (data_cnt != (DNL_WF_PATH_CR_NUM * WF_NUM))
        {
            DBG("Over retry(%d)! DNL Cal Result Fail\n", QUERY_RECAL_RETRY_CNT);
            ret = META_WIFI_STATUS_FAIL;
            goto error;
        }



        for (i = 0 ; i < data_cnt ; i++)
        {
            DBG("(%d)ID=0x%X,Addr=0x%X,Val=0x%X\n", i, rReCalInfo.u4CalId[i], rReCalInfo.u4CalAddr[i], rReCalInfo.u4CalValue[i]);
        }

        for (i = 0; i < WF_NUM; i++)
        {

            crOfsetIdx = DNL_WF_PATH_CR_NUM * i;
            memset(&rDnl, 0, sizeof(WIFI_NVRAM_TX_DNL_T));

            rDnl.ucTxDnlCckGT0 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT0 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT0 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT0 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT0 = (rReCalInfo.u4CalValue[crOfsetIdx + 1] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT1 = (rReCalInfo.u4CalValue[crOfsetIdx + 2] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT1 = (rReCalInfo.u4CalValue[crOfsetIdx + 2] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT1 = (rReCalInfo.u4CalValue[crOfsetIdx + 2] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT1 = (rReCalInfo.u4CalValue[crOfsetIdx + 2] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT1 = (rReCalInfo.u4CalValue[crOfsetIdx + 3] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT2 = (rReCalInfo.u4CalValue[crOfsetIdx + 4] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT2 = (rReCalInfo.u4CalValue[crOfsetIdx + 4] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT2 = (rReCalInfo.u4CalValue[crOfsetIdx + 4] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT2 = (rReCalInfo.u4CalValue[crOfsetIdx + 4] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT2 = (rReCalInfo.u4CalValue[crOfsetIdx + 5] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT3 = (rReCalInfo.u4CalValue[crOfsetIdx + 6] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT3 = (rReCalInfo.u4CalValue[crOfsetIdx + 6] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT3 = (rReCalInfo.u4CalValue[crOfsetIdx + 6] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT3 = (rReCalInfo.u4CalValue[crOfsetIdx + 6] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT3 = (rReCalInfo.u4CalValue[crOfsetIdx + 7] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT4 = (rReCalInfo.u4CalValue[crOfsetIdx + 8] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT4 = (rReCalInfo.u4CalValue[crOfsetIdx + 8] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT4 = (rReCalInfo.u4CalValue[crOfsetIdx + 8] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT4 = (rReCalInfo.u4CalValue[crOfsetIdx + 8] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT4 = (rReCalInfo.u4CalValue[crOfsetIdx + 9] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT5 = (rReCalInfo.u4CalValue[crOfsetIdx + 10] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT5 = (rReCalInfo.u4CalValue[crOfsetIdx + 10] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT5 = (rReCalInfo.u4CalValue[crOfsetIdx + 10] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT5 = (rReCalInfo.u4CalValue[crOfsetIdx + 10] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT5 = (rReCalInfo.u4CalValue[crOfsetIdx + 11] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT6 = (rReCalInfo.u4CalValue[crOfsetIdx + 12] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT6 = (rReCalInfo.u4CalValue[crOfsetIdx + 12] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT6 = (rReCalInfo.u4CalValue[crOfsetIdx + 12] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT6 = (rReCalInfo.u4CalValue[crOfsetIdx + 12] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT6 = (rReCalInfo.u4CalValue[crOfsetIdx + 13] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;

            rDnl.ucTxDnlCckGT7 = (rReCalInfo.u4CalValue[crOfsetIdx + 14] & NDL_OFFSET_BIAS0_MASK) >> NDL_OFFSET_BIAS0_SHFT;
            rDnl.ucTxDnlLowGT7 = (rReCalInfo.u4CalValue[crOfsetIdx + 14] & NDL_OFFSET_BIAS1_MASK) >> NDL_OFFSET_BIAS1_SHFT;
            rDnl.ucTxDnlMidGT7 = (rReCalInfo.u4CalValue[crOfsetIdx + 14] & NDL_OFFSET_BIAS2_MASK) >> NDL_OFFSET_BIAS2_SHFT;
            rDnl.ucTxDnlHighGT7 = (rReCalInfo.u4CalValue[crOfsetIdx + 14] & NDL_OFFSET_BIAS3_MASK) >> NDL_OFFSET_BIAS3_SHFT;
            rDnl.ucTxDnlUltraGT7 = (rReCalInfo.u4CalValue[crOfsetIdx + 15] & NDL_OFFSET_BIAS4_MASK) >> NDL_OFFSET_BIAS4_SHFT;


            if (eBand == BAND_2G4)
            {
                //2.4G channel all the same DNL value;
                for (chIdx = 0 ; chIdx < 14 ; chIdx++)
                {
                    nvramOfs = (i == WF0) ? (NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF0) : (NVRAM_G_BAND_TSSI_DNL_OFS_OFSETOF_WF1);
                    //get nvram by ch group offset
                    nvramOfs += sizeof(WIFI_NVRAM_TX_DNL_T) * chIdx;
                    memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

                    pQueryNv->dataLen = sizeof(WIFI_NVRAM_TX_DNL_T);
                    pQueryNv->dataOffset = nvramOfs;
                    memcpy(&pQueryNv->data[0], &rDnl, sizeof(WIFI_NVRAM_TX_DNL_T));
                    wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

                    DBG("WF%d update B[%d] Ch=%d nvramOfs=0x%08X done!\n", i, eBand, chIdx, nvramOfs);
                }
            }
            else
            {
                //get 5G DNL offset
                nvramOfs = (i == WF0) ? (NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_DNL_OFS_OFSETOF_WF1);

                //get nvram by ch group offset
                nvramOfs += sizeof(WIFI_NVRAM_TX_DNL_T) * chGroup;
                memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

                pQueryNv->dataLen = sizeof(WIFI_NVRAM_TX_DNL_T);
                pQueryNv->dataOffset = nvramOfs;
                memcpy(&pQueryNv->data[0], &rDnl, sizeof(WIFI_NVRAM_TX_DNL_T));
                wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

                DBG("WF%d update B[%d] Ch=%d Group =%d nvramOfs=0x%08X done!\n", i, eBand, inputCh, chGroup, nvramOfs);

            }


        }

        ret = META_WIFI_STATUS_SUCCESS;

        DBG("DNL Adjust success!\n");
    error:
        FREEIF(pQueryNv);
    }else {

        DBG("[WARNING] Adjust not-support!\n");
    }

    return ret;
}


/*LNA(Rssi) Gain Calibration clear*/
WLAN_STATUS wifiNVRAMLnaGainCalClear(void)
{
    NVRAM_ACCESS_STRUCT raNvram[] =
    {
        /*2.4G WF0 LNA(Rssi) Gain Calibration*/
        {NVRAM_G_BAND_LNA_GAIN_CAL_OFS_SIZE, NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0, {0}},
        /*2.4G WF1 LNA(Rssi) Gain Calibration*/
        {NVRAM_G_BAND_LNA_GAIN_CAL_OFS_SIZE, NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1, {0}},
        /*5G WF0 LNA(Rssi) Gain Calibration*/
        {NVRAM_A_BAND_LNA_GAIN_CAL_OFS_SIZE, NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0, {0}},
        /*5G WF1 LNA(Rssi) Gain Calibration*/
        {NVRAM_A_BAND_LNA_GAIN_CAL_OFS_SIZE, NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1, {0}},

    };
    PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
    char *pBuf = NULL;
    int i = 0;

    if (!IS_WIFI_GEN_VER_SOC3_0())
        return META_WIFI_STATUS_FAIL;

    pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

    if (pQueryNv == NULL)
    {
        ERR("[META_WIFI] No memory nvram\n");
        goto error;
    }

    for (i = 0 ; i < (sizeof(raNvram) / sizeof(NVRAM_ACCESS_STRUCT)) ; i++)
    {
        pQueryNv->dataLen = raNvram[i].dataLen;
        pQueryNv->dataOffset = raNvram[i].dataOffset;
        pBuf = (char *)&pQueryNv->data[0];

        /*clear NVRAM content*/
        memset(pBuf, 0, MAX_NVRAM_ACCESS_SIZE);
        wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

    }

    DBG("Finish!\n");
error:
    FREEIF(pQueryNv);

    return META_WIFI_STATUS_SUCCESS;

}
/*LNA(Rssi) Gain Calibration Adjust*/
WLAN_STATUS wifiNVRAMLnaGainCalAdjust(unsigned int dbdcbandIdx,
                                      ENUM_BAND_T eBand, unsigned char inputCh)
{

    unsigned char chGroup = 0;
    unsigned int data_cnt = 0;
    char crOfsetIdx = 0;
    int i = 0;
    RECAL_INFO_T rReCalInfo;
    WIFI_NVRAM_LNA_GAIN_CAL_T rLnaGain;
    unsigned int nvramOfs = 0;
    PNVRAM_ACCESS_STRUCT pQueryNv = NULL;
    WLAN_STATUS ret = META_WIFI_STATUS_FAIL;

    if (!IS_WIFI_GEN_VER_SOC3_0())
        return META_WIFI_STATUS_FAIL;

    memset(&rReCalInfo, 0, sizeof(RECAL_INFO_T));
    memset(&rLnaGain, 0, sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T));

    pQueryNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

    //get ch group
    chGroup = wifiNVRAMGetChGroup(eBand, inputCh, CH_GROUP_ITEM_COMMON);

    DBG("DbdcIdx=%d,Band=%d,ch=%d,Group=%d\n", dbdcbandIdx, eBand, inputCh, chGroup);

    for (i = 0 ; i < QUERY_RECAL_RETRY_CNT ; i++)
    {

        //set Lna/Rssi Gain Cal by HQA CMD
        if (wifiHqaDoCalibrationTestItem(HQA_CAL_ITEM_LNA_GIAN_CAL, dbdcbandIdx) != META_WIFI_STATUS_SUCCESS)
            DBG("Cal test item fail!\n");


        //query  Lna/Rssi Gain Cal RESULT
        if (wifiHqaGetDumpReCal(HQA_CAL_ITEM_LNA_GIAN_CAL, dbdcbandIdx, &rReCalInfo) != META_WIFI_STATUS_SUCCESS)
            DBG("Cal test item fail!\n");


        data_cnt = rReCalInfo.u4Count;

        if (data_cnt != (LNA_GIAN_CAL_WF_PATH_CR_NUM * WF_NUM))
        {
            DBG("Result Num dismatch!,Expect:%d Total:%d do-try(%d)!\n",
                (LNA_GIAN_CAL_WF_PATH_CR_NUM * WF_NUM),
                data_cnt, i);
            continue;
        }
        else
            break;
    }

    if (data_cnt != (LNA_GIAN_CAL_WF_PATH_CR_NUM * WF_NUM))
    {
        DBG("Over retry(%d)! DNL Cal Result Fail\n", QUERY_RECAL_RETRY_CNT);
        ret = META_WIFI_STATUS_FAIL;
        goto error;
    }

    for (i = 0 ; i < data_cnt ; i++)
    {
        DBG("(%d)ID=0x%X,Addr=0x%X,Val=0x%X\n", i, rReCalInfo.u4CalId[i], rReCalInfo.u4CalAddr[i], rReCalInfo.u4CalValue[i]);
    }

    for (i = 0; i < WF_NUM; i++)
    {

        crOfsetIdx = LNA_GIAN_CAL_WF_PATH_CR_NUM * i;

        //get Cal value
        rLnaGain.ucRxCal1 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & LNA_GAIN_TAB0_MASK) >> LNA_GAIN_TAB0_SHFT;
        rLnaGain.ucRxCal2 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & LNA_GAIN_TAB1_MASK) >> LNA_GAIN_TAB1_SHFT;
        rLnaGain.ucRxCal3 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & LNA_GAIN_TAB2_MASK) >> LNA_GAIN_TAB2_SHFT;
        rLnaGain.ucRxCal4 = (rReCalInfo.u4CalValue[crOfsetIdx + 0] & LNA_GAIN_TAB3_MASK) >> LNA_GAIN_TAB3_SHFT;
        rLnaGain.ucRxCal5 = (rReCalInfo.u4CalValue[crOfsetIdx + 1] & LNA_GAIN_TAB4_MASK) >> LNA_GAIN_TAB4_SHFT;
        rLnaGain.ucRxCal6 = 0; /*reserved*/

        //calculate nvram offset
        if (eBand == BAND_2G4)
        {
            nvramOfs = (i == WF0) ? (NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0) : (NVRAM_G_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1);
        }
        else
        {
            nvramOfs = (i == WF0) ? (NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_LNA_GAIN_CAL_OFS_OFSETOF_WF1);
            //get nvram by ch group offset
            nvramOfs += sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T) * chGroup;
        }


        memset(pQueryNv, 0, sizeof(NVRAM_ACCESS_STRUCT) + MAX_NVRAM_ACCESS_SIZE);

        pQueryNv->dataLen = sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T);
        pQueryNv->dataOffset = nvramOfs;
        memcpy(&pQueryNv->data[0], &rLnaGain, sizeof(WIFI_NVRAM_LNA_GAIN_CAL_T));
        wifiNVRAMCtrl(NVRAM_WRITE, pQueryNv);

        DBG("WF%d update B[%d] Ch=%d GP=%d nvramOfs=0x%08X done!\n", i, eBand, inputCh, chGroup, nvramOfs);
    }

    ret = META_WIFI_STATUS_SUCCESS;

    DBG("Success!\n");
error:
    FREEIF(pQueryNv);

    return ret;
}


WLAN_STATUS wifiNVRAMTssiChOfsInterpolation(TX_PWR_INTERPOLATION_TYPE type,
        ENUM_BAND_T eBand, unsigned int chNum, unsigned int *pchArray)
{

    int wf = WF0;
    unsigned int u4NvramOffset;
    unsigned int chIdx = 0, rfIdx = 0;
    int ch0 = 0, ch1 = 0, chInter = 0;
    unsigned int readLine = 0;
    int devStep = 0;
    char nvCompVal = 0;
    unsigned char rNvVal[NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2];
    PNVRAM_ACCESS_STRUCT pSetNv = NULL;
    int chOfs0 = 0, chOfs1 = 0;
    unsigned char chGroup[NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2];
    unsigned char rfGroup = 0;
    INTER_ACT interAct = INTER_ACT_NOT_SUPPORT;
    INTERPOLATION_CH_BOUND_A_BAND rfInterList[NVRAM_TSSI_CH_OFFSET_A_BAND_RF_GROUP_NUM] =
    {
        {0xFF, 0}, //RF Group 0, not support 5G Interpolation
        {36, 48}, //RF Group 1
        {52, 64}, //RF Group 2
        {0xFF, 0}, //RF Group 3, not support 5G Interpolation
        {100, 112}, //RF Group 4
        {116, 128}, //RF Group 5
        {132, 144}, //RF Group 6
        {149, 165}, //RF Group 7
    };

    /*init */
    pSetNv = (PNVRAM_ACCESS_STRUCT)malloc(sizeof(NVRAM_ACCESS_STRUCT) + sizeof(unsigned char));
    if (!pSetNv)
    {
        DBG("out of memory in allocating pSetNv\n");
        return META_WIFI_STATUS_FAIL;
    }
    DBG("Band[%d] chNum[%d] Type[%d] Enter\n", eBand, chNum, type);
    memset(&rNvVal[0], 0, sizeof(unsigned char) * (NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2));
    memset(&chGroup[0], 0, sizeof(unsigned char) * (NVRAM_TSSI_CH_OFFSET_A_BAND_CH_GROUP_NUM * 2));

    for (wf = WF0; wf < WF_NUM; wf++)
    {

        //read GT table by channel range
        for (chIdx = 0; chIdx < chNum; chIdx++)
        {
            ch0 = pchArray[chIdx];
            chGroup[chIdx] = wifiNVRAMGetChGroup(eBand, ch0, CH_GROUP_ITEM_TSSI_CH);

            if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, ch0, &u4NvramOffset) != META_WIFI_STATUS_SUCCESS)
            {
                DBG("Get Ch offset fial!\n");
                goto error;
            }

            pSetNv->dataLen = sizeof(unsigned char);
            pSetNv->dataOffset = u4NvramOffset;
            readLine = wifiNVRAMCtrl(NVRAM_READ, pSetNv);
            memcpy(&rNvVal[chIdx], &pSetNv->data[0], sizeof(unsigned char));
            DBG("Read WF[%d]chIdx[%d] = %d,Group=%d, Ofs=[0x%X] readLen=%d\n",
                wf,
                chIdx,
                ch0,
                chGroup[chIdx],
                rNvVal[chIdx],
                readLine);
        }



        /*only 2.4G support interpolation -- start*/
        if ((type == TYPE_INTERPOLATION) && (eBand == BAND_2G4))
        {
            //calcaute each step ch=0 ~ pchArray(chNum-1)
            for (chIdx = 0; chIdx < chNum - 1; chIdx++)
            {

                ch0 = pchArray[chIdx];
                chOfs0 =  SIGNED_CONVERT_EXTEND_BITS(rNvVal[chIdx], 8);


                ch1 = pchArray[(chIdx + 1)];
                chOfs1 = SIGNED_CONVERT_EXTEND_BITS(rNvVal[(chIdx + 1)], 8);

                for (chInter = ch0 + 1 ; chInter < ch1; chInter++)
                {
                    u4NvramOffset = 0;

                    if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, chInter, &u4NvramOffset)
                            != META_WIFI_STATUS_SUCCESS)
                    {
                        DBG("Get Ch offset fial!\n");
                        goto error;
                    }

                    devStep = ((chOfs1 - chOfs0) * (chInter - ch0)) / (ch1 - ch0);

                    DBG("Interpolation ch[%d ~ %d] tssiChVal=[%d ~ %d],ch[%d] + devStep=%d\n",
                        ch0, ch1, chOfs0, chOfs1, chInter, devStep);

                    nvCompVal = (chOfs0 + devStep) & 0x000000FF;

                    if (IS_CFG_TSSI_CH_GT_SAME()) {
                        wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                    } else {
                        wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
                        DBG("ch[%d] nvOfs=0x%X,nvCompVal=0x%02X\n",
                            chInter, u4NvramOffset, nvCompVal);
                    }
                }
            }

            //calcaute each step pchArray(n+1) ~ ch=14
            for (chInter = ch1 + 1 ; chInter <= 14; chInter++)
            {
                u4NvramOffset = 0;

                if (wifiNVRAMTssiChGetNVRAMOfs(wf, eBand, chInter, &u4NvramOffset) != META_WIFI_STATUS_SUCCESS)
                {
                    DBG("Get Ch offset fial!\n");
                    goto error;
                }

                devStep = ((chOfs1 - chOfs0) * (chInter - ch1)) / (ch1 - ch0);

                DBG("Extrapolation ch[%d ~ %d] tssiChVal=[%d ~ %d],ch[%d] + devStep=%d\n",
                    ch1 + 1, 14, chOfs0, chOfs1, chInter, devStep);
                nvCompVal = chOfs0 + (chInter - ch0) * devStep;

                if (IS_CFG_TSSI_CH_GT_SAME()) {
                    wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                } else {
                    wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);
                    DBG("ch[%d] nvOfs=0x%X,nvCompVal=0x%02X\n",
                        chInter, u4NvramOffset, nvCompVal);
                }

            }
        }
        else if ((type == TYPE_GROUP_THE_SAME) && (eBand == BAND_5G))
        {
            /* "group the same" means using meanuse tx power value to apply tssi ch ofs group
                         *   in RF defined group
                         */
            //calcaute each step ch=0 ~ pchArray(chNum)
            for (chIdx = 0; chIdx < chNum ; chIdx++)
            {
                nvCompVal = rNvVal[chIdx];
                wifiNVRAMTssiChOfsRfGroupTheSame(wf, eBand, pchArray[chIdx], chGroup[chIdx], nvCompVal);
            }
        }
        else if ((type == TYPE_INTERPOLATION) && (eBand == BAND_5G))
        {
            //calcaute each step ch=0 ~ pchArray(chNum-1)
            for (chIdx = 0; chIdx < chNum; chIdx++)
            {
                interAct = INTER_ACT_NOT_SUPPORT;

                //Check 5G interpolation support RF group channel bound
                for (rfIdx = 0; rfIdx < NVRAM_TSSI_CH_OFFSET_A_BAND_RF_GROUP_NUM; rfIdx++)
                {
                    DBG("ch=%d Check RFGroup[%d] bound[%d ~ %d]\n",
                        pchArray[chIdx],
                        rfIdx,
                        rfInterList[rfIdx].lowBoundCh,
                        rfInterList[rfIdx].upperBoundCh);

                    //if chIdx = low bound  and chIdx+1 = upper bound
                    if (pchArray[chIdx] == rfInterList[rfIdx].lowBoundCh)
                    {
                        if ((chIdx < (chNum - 1)) &&
                                pchArray[chIdx + 1] == rfInterList[rfIdx].upperBoundCh)
                        {
                            interAct = INTER_ACT_INTERPOLATION;

                            rfGroup = GET_A_BAND_RF_GROUP(chGroup[chIdx]);

                            ch0 = pchArray[chIdx];
                            ch1 = pchArray[(chIdx + 1)];

                            chOfs0 =  SIGNED_CONVERT_EXTEND_BITS(rNvVal[chIdx], 8);
                            chOfs1 = SIGNED_CONVERT_EXTEND_BITS(rNvVal[(chIdx + 1)], 8);

                            DBG("5G do Interpolation WF[%d] RF[%d] ch[%d ~ %d] Rang[%d ~ %d]\n",
                                wf,
                                rfGroup,
                                ch0,
                                ch1,
                                chOfs0,
                                chOfs1);

                            //Query the next RF group
                            chIdx++;
                            break;
                        }
                        else
                        {
                            interAct = INTER_ACT_GROUP_THE_SAME;
                            break;
                        }
                    }
                    else if ((pchArray[chIdx] > rfInterList[rfIdx].lowBoundCh) &&
                             (pchArray[chIdx] < rfInterList[rfIdx].upperBoundCh))
                    {
                        interAct = INTER_ACT_GROUP_THE_SAME;
                        break;
                    }
                    else
                        interAct = INTER_ACT_GROUP_THE_SAME;

                }

                if (interAct == INTER_ACT_GROUP_THE_SAME)
                {
                    nvCompVal = rNvVal[chIdx];
                    wifiNVRAMTssiChOfsRfGroupTheSame(wf, eBand, pchArray[chIdx], chGroup[chIdx], nvCompVal);
                }
                else if (interAct == INTER_ACT_INTERPOLATION)
                {

                    /*update (n) ~ (n+3) Tssi ch group in one RF Group*/
                    for (chInter = (rfGroup * PER_CH_GROUP_IN_RF_GROUP) ; chInter < ((rfGroup * PER_CH_GROUP_IN_RF_GROUP) + PER_CH_GROUP_IN_RF_GROUP); chInter++)
                    {
                        u4NvramOffset = (wf == WF0) ? (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF0) : (NVRAM_A_BAND_TSSI_CH_OFS_OFSETOF_WF1);
                        u4NvramOffset += (chInter * sizeof(unsigned char));

                        devStep = ((chOfs1 - chOfs0) * (chInter - (rfGroup * PER_CH_GROUP_IN_RF_GROUP))) / (PER_CH_GROUP_IN_RF_GROUP - 1);

                        nvCompVal = (chOfs0 + devStep) & 0x000000FF;

                        DBG("Interpolation:TssiChGroup[%d][%c] + devStep=%d,NvOfs=0x%08X,NvVal=0x%02X\n",
                            chInter / 2,
                            ((chInter % 2) == 0) ? ('L') : ('H'),
                            devStep,
                            u4NvramOffset,
                            nvCompVal);

                        if (IS_CFG_TSSI_CH_GT_SAME())
                            wifiNVRAMTssiChGainTableAllTheSame(u4NvramOffset, nvCompVal);
                        else
                            wifiNVRAMWriteByte(u4NvramOffset, nvCompVal);

                    }

                }
                else
                    DBG("Not Support ch[%d]!\n", pchArray[chIdx + 1]);

            }
        }
    }

    FREEIF(pSetNv);

    return META_WIFI_STATUS_SUCCESS;

error:
    FREEIF(pSetNv);
    return META_WIFI_STATUS_FAIL;
}


WLAN_STATUS wifiProductLineCalProcess(P_CMD_PL_CAL pCmdPLcal)
{
    WLAN_STATUS ret = META_WIFI_STATUS_FAIL;
    unsigned char i = 0;
    unsigned int arg[MAX_PL_INPUT_ARG_NUM] = {0};
    unsigned int CalStatus = META_WIFI_STATUS_FAIL;
    unsigned int targetPwr = gHqaParaInfo.power;
    int meanPwr = 0;
    unsigned int ch = 0;
    unsigned int wf_idx = -1;
    unsigned int dbdcBandIdx = -1;
    ENUM_BAND_T eBand = (gHqaParaInfo.chBand == 0) ? (BAND_2G4) : (BAND_5G);

    unsigned int band_chNum = 0;
    unsigned int interpolat_type = 0;


    memset(&arg[0], 0, sizeof(unsigned int)*MAX_PL_INPUT_ARG_NUM);

    if (gHqaParaInfo.eCbw == CDBW_80P80)
        ch = gHqaParaInfo.chS2;
    else
        ch = gHqaParaInfo.chS1;

    wf_idx = gHqaParaInfo.wf_idx;

    dbdcBandIdx = gHqaParaInfo.dbdcBandIdx;

    if (pCmdPLcal == NULL)
    {
        ERR("pCmdPLcal is null\n");
        ret = META_WIFI_STATUS_INVALID_PARA;
        return ret;
    }

    DBG("ID=%d Act=%d ParaNum= %d wf_idx=%d\n", pCmdPLcal->calId, pCmdPLcal->action, pCmdPLcal->inputLen, wf_idx);

    for (i = 0; i < pCmdPLcal->inputLen ; i++)
    {
        arg[i] = pCmdPLcal->au4Buffer[i];
        DBG("Para (%d)=%d\n", i, arg[i]);
    }

    switch (pCmdPLcal->calId)
    {

        case WIFI_PL_CAL_TX_PWR:
            {
                switch (pCmdPLcal->action)
                {
                    case TX_PWR_CAL_ACT_START:
                        ret = wifiNVRAMTssiChOfsClear();

                        if (ret == META_WIFI_STATUS_SUCCESS)
                        {
                            wifwNVRAMWriteDataToDriver();
                            usleep(500000);
                        }

                        break;

                    case TX_PWR_CAL_ACT_ADJUST:
                        meanPwr = arg[0];

                        if (wf_idx & BIT(WF0))
                            ret = wifiNVRAMTssiChOfsAdjust(WF0, eBand, ch, targetPwr, meanPwr);

                        if (wf_idx & BIT(WF1))
                            ret = wifiNVRAMTssiChOfsAdjust(WF1, eBand, ch, targetPwr, meanPwr);

                        if (ret == META_WIFI_STATUS_SUCCESS)
                        {
                            wifwNVRAMWriteDataToDriver();
                            usleep(500000);
                        }

                        break;

                    case TX_PWR_CAL_ACT_END:
                        CalStatus = arg[0];

                        if (CalStatus == META_WIFI_STATUS_SUCCESS)
                        {
                            DBG("[META_WIFI] TX POWER CALIBRATION PASS!\n");
                            ret = META_WIFI_STATUS_SUCCESS;
                        }
                        else
                        {
                            DBG("[META_WIFI] TX POWER CALIBRATION Fail! Clear Result!\n");
                            ret = wifiNVRAMTssiChOfsClear();
                        }

                        break;

                    case TX_PWR_CAL_ACT_INTERPOLAT:
                        if (arg[0] & BIT(0)) //BIT[0]:2.4G
                            eBand = BAND_2G4;
                        else if (arg[0] & BIT(1))//BIT[1]:5G
                            eBand = BAND_5G;

                        DBG("[META_WIFI] TX_PWR_CAL_ACT_INTERPOLAT Band(%d)\n", eBand);
                        interpolat_type = arg[1];
                        band_chNum = arg[2];
                        if (IS_WIFI_GEN_VER_SOC3_0())
                            ret = wifiNVRAMTssiChOfsInterpolation_soc30(interpolat_type, eBand, band_chNum, &arg[3]);
                        else
                            ret = wifiNVRAMTssiChOfsInterpolation(interpolat_type, eBand, band_chNum, &arg[3]);

                        if (ret == META_WIFI_STATUS_SUCCESS)
                        {
                            wifwNVRAMWriteDataToDriver();
                            usleep(500000);
                        }

                        return wifiNVRAMTssiContentDumpToPC(eBand, pCmdPLcal, ret);

                    default:
                        DBG("[META_WIFI] un-support act(%d)\n", pCmdPLcal->action);
                        ret = META_WIFI_STATUS_FAIL;
                        goto error;
                }
            }
            break;

        case WIFI_PL_CAL_EPA_FE_GAIN:
            break;

        case WIFI_PL_CAL_LNA_GAIN_CAL:
            switch (pCmdPLcal->action)
            {
                case TX_PWR_CAL_ACT_START:
                    ret = wifiNVRAMLnaGainCalClear();

                    if (ret == META_WIFI_STATUS_SUCCESS)
                    {
                        wifwNVRAMWriteDataToDriver();
                        usleep(500000);
                    }

                    break;

                case TX_PWR_CAL_ACT_ADJUST:
                    ret = wifiNVRAMLnaGainCalAdjust(dbdcBandIdx, eBand, ch);

                    if (ret == META_WIFI_STATUS_SUCCESS)
                    {
                        wifwNVRAMWriteDataToDriver();
                        usleep(500000);
                    }

                    break;

                case TX_PWR_CAL_ACT_END:
                    CalStatus = arg[0];

                    if (CalStatus == META_WIFI_STATUS_SUCCESS)
                    {
                        DBG("[META_WIFI] LNA GAIN CALIBRATION PASS!\n");
                        ret = META_WIFI_STATUS_SUCCESS;
                    }
                    else
                    {
                        DBG("[META_WIFI] LNA GAIN CALIBRATION Fail! Clear Result!\n");
                        ret = wifiNVRAMLnaGainCalClear();
                    }

                    break;

                default:
                    DBG("[META_WIFI] un-support act(%d)\n", pCmdPLcal->action);
                    ret = META_WIFI_STATUS_FAIL;
                    goto error;

            }

            break;

        case WIFI_PL_CAL_DNL_CAL:
            switch (pCmdPLcal->action)
            {
                case TX_PWR_CAL_ACT_START:
                    ret = wifiNVRAMTssiNDLOfsClear();

                    if (ret == META_WIFI_STATUS_SUCCESS)
                    {
                        wifwNVRAMWriteDataToDriver();
                        usleep(500000);
                    }

                    break;

                case TX_PWR_CAL_ACT_ADJUST:
                    ret = wifiNVRAMTssiDnlOfsAdjust(dbdcBandIdx, eBand, ch);

                    if (ret == META_WIFI_STATUS_SUCCESS)
                    {
                        wifwNVRAMWriteDataToDriver();
                        usleep(500000);
                    }

                    break;

                case TX_PWR_CAL_ACT_END:
                    CalStatus = arg[0];

                    if (CalStatus == META_WIFI_STATUS_SUCCESS)
                    {
                        DBG("[META_WIFI] DNL CALIBRATION PASS!\n");
                        ret = META_WIFI_STATUS_SUCCESS;
                    }
                    else
                    {
                        DBG("[META_WIFI] DNL CALIBRATION Fail! Clear Result!\n");
                        ret = wifiNVRAMTssiNDLOfsClear();
                    }

                    break;

                default:
                    DBG("[META_WIFI] un-support act(%d)\n", pCmdPLcal->action);
                    ret = META_WIFI_STATUS_FAIL;
                    goto error;

            }

            break;

        default:
            ERR("Not Support Cal ID :%d\n", pCmdPLcal->calId);
            ret = META_WIFI_STATUS_FAIL;
            goto error;
    }

error:

    //reponse Product Line calibration result
    pCmdPLcal->inputLen = sizeof(ret);
    pCmdPLcal->au4Buffer[0] = ret;

    return ret;

}

WLAN_STATUS wifiProductLineScript(char* pCmd, unsigned short pCmdlen)
{
    CMD_PL_CAL rCmdPlCal;
    unsigned int act;
    unsigned int val;
    unsigned int calId;
    int rev = 0;

    DBG("do Product line cmd: %s len:%d\n", pCmd, pCmdlen);
    rev = sscanf(pCmd, "PL %d %d %d", &calId, &act, &val);
    if (rev < 3)
    {
        DBG("ERROR format:CAL ID:%d,ACT:%d,VAL:%d\n", calId, act, val);
        return META_WIFI_STATUS_INVALID_PARA;
    }
    DBG("rev:%d, CAL ID:%d,ACT:%d,VAL:%d\n", rev, calId, act, val);

    rCmdPlCal.calId = calId;
    rCmdPlCal.flags = FALSE;
    rCmdPlCal.action = act;
    rCmdPlCal.inputLen = 1;
    rCmdPlCal.au4Buffer[0] = val;

    return wifiProductLineCalProcess(&rCmdPlCal);

}

WLAN_STATUS wifiProductInit(void)
{
    memset(&gHqaParaInfo, 0, sizeof(gHqaParaInfo));

    return META_WIFI_STATUS_SUCCESS;

}

unsigned int wifiHqaGetParaAndShiftBuf(
    bool convert, unsigned int size, unsigned char **buf, unsigned char *out)
{
    if (!(*buf))
    {
        DBG("*buf NULL pointer with size=%u\n", size);
        return META_WIFI_STATUS_INVALID_PARA;
    }

    if (!out)
    {
        DBG("out NULL pointer with size=%u\n", size);
        return META_WIFI_STATUS_INVALID_PARA;
    }

    memcpy(out, *buf, size);
    *buf = *buf + size;

    if (!convert)
    {
        DBG("size=%u", size);
        return META_WIFI_STATUS_SUCCESS;
    }

    if (size == sizeof(unsigned int))
    {
        unsigned int *tmp = (unsigned int *) out;

        *tmp = ntohl(*tmp);
        DBG("size=%u, val=%u\n", size, *tmp);
    }
    else if (size == sizeof(unsigned short))
    {
        unsigned short *tmp = (unsigned short *) out;

        *tmp = ntohs(*tmp);
        DBG("size=%u, val=%u\n", size, *tmp);
    }
    else
    {
        DBG("size %u not supported\n", size);
        return META_WIFI_STATUS_NOT_SUPPORT;
    }

    return META_WIFI_STATUS_SUCCESS;
}

WLAN_STATUS wifiHqaGetDumpReCal(unsigned int item, unsigned int dbdcBandIdx, P_RECAL_INFO_T prReCalInfo)
{
    P_HQA_CMD_FRAME prHqaCmd = NULL;
    WLAN_STATUS ret = META_WIFI_STATUS_FAIL;
    unsigned char* pData = NULL;


    unsigned int avail_sz = 0;
    int i = 0, j = 0;
    int u4hqaCmdLen = 0;

    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(RECAL_INFO_T) + sizeof(RECAL_INFO_T);
    prHqaCmd = (P_HQA_CMD_FRAME)malloc(u4hqaCmdLen);
    if (!prHqaCmd)
    {
        DBG("out of memory in allocating prHqaCmd\n");
        return META_WIFI_STATUS_FAIL;
    }
    memset(prHqaCmd, 0, u4hqaCmdLen);


    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_GetDumpRecal);
    prHqaCmd->length = htons(sizeof(unsigned int));
    prHqaCmd->sequence = 1;
    dbdcBandIdx = htonl(dbdcBandIdx);

    memcpy(&prHqaCmd->data[0], &dbdcBandIdx, sizeof(unsigned int));

    for (i = 0; i < POLLING_RECAL_RETRY_CNT; i++)
    {
        ret = HQAIWreq(wifi_skfd, "wlan0", (char *)prHqaCmd, u4hqaCmdLen, &avail_sz);

        DBG("[HQA_CMD] dump Item:%d,BandIdx:%d,CMD:%p,len:%d,ret:%d,avail_sz:%d\n",
            item,
            dbdcBandIdx,
            prHqaCmd,
            u4hqaCmdLen,
            ret,
            avail_sz);

        if (ret != META_WIFI_STATUS_SUCCESS)
        {
            DBG("[HQA_CMD] dump Item:%d fail!ret(%d)\n", item, ret);
            break;
        }

        if (avail_sz > 0)
        {
            pData = (unsigned char*)&prHqaCmd->data[0];

            //status 2Byte
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned short), &pData, (unsigned char *)&prReCalInfo->status);

            //Count 4Byte
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&prReCalInfo->u4Count);

            DBG("[HQA_CMD] dump Cal Item:%d,retry:%d,status:%d,Count:%d\n", item, i, prReCalInfo->status, prReCalInfo->u4Count);


            if (prReCalInfo->u4Count == 0)
            {
                usleep(1 * 1000 * 1000);
                DBG("[HQA_CMD] dump Item Sleep 1s do next query\n");
            }
            else if (prReCalInfo->u4Count > MAX_RECAL_DATA_NUM)
            {
                DBG("[HQA_CMD] prReCalInfo->u4Count out of bound!\n");
                ret = META_WIFI_STATUS_FAIL;
                goto error;
            }
            else
            {

                for (j = 0 ; j < prReCalInfo->u4Count ; j++)
                {
                    //Recal ID 4Byte
                    wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&prReCalInfo->u4CalId[j]);
                    //Offset 4Byte
                    wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&prReCalInfo->u4CalAddr[j]);
                    //Value 4Byte
                    wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&prReCalInfo->u4CalValue[j]);

                    DBG("[HQA_CMD] (%d)ID=0x%08X,CR=0x%08X,Value=0x%08X\n"
                        , j, prReCalInfo->u4CalId[j], prReCalInfo->u4CalAddr[j], prReCalInfo->u4CalValue[j]);
                }

                ret = META_WIFI_STATUS_SUCCESS;

                break;
            }
        }

    }

error:
    FREEIF(prHqaCmd);

    DBG("finish\n");

    return ret;
}
WLAN_STATUS wifiHqaDoCalibrationTestItem(unsigned int item, unsigned int dbdcBandIdx)
{
    P_HQA_CMD_FRAME prHqaCmd;
    WLAN_STATUS ret = META_WIFI_STATUS_FAIL;
    HQA_DO_CAL_TEST_ITEM rCalItem;

    unsigned int avail_sz = 0;
    int u4hqaCmdLen = 0;

    u4hqaCmdLen = sizeof(HQA_CMD_FRAME) + sizeof(HQA_DO_CAL_TEST_ITEM);
    prHqaCmd = (P_HQA_CMD_FRAME)malloc(u4hqaCmdLen);
    if (!prHqaCmd)
    {
        DBG("out of memory in allocating prHqaCmd\n");
        return META_WIFI_STATUS_FAIL;
    }
    memset(prHqaCmd, 0, u4hqaCmdLen);


    prHqaCmd->magicNo = htonl(HQA_CMD_MAGIC_NO);
    prHqaCmd->type = 0;
    prHqaCmd->id = htons(HQA_CMD_DoCalibrationTestItem);
    prHqaCmd->length = htons(sizeof(rCalItem));
    prHqaCmd->sequence = 1;
    rCalItem.item = htonl(item);
    rCalItem.band_idx = htonl(dbdcBandIdx);

    memcpy(&prHqaCmd->data[0], &rCalItem, sizeof(HQA_DO_CAL_TEST_ITEM));

    ret = HQAIWreq(wifi_skfd, "wlan0", (char *)prHqaCmd, u4hqaCmdLen, &avail_sz);

    DBG("[HQA_CMD] Do Cal Item:0x%08X,BandIdx:%d,CMD:%p,len:%d,ret:%d,avail_sz:%d\n",
        item,
        dbdcBandIdx,
        prHqaCmd,
        u4hqaCmdLen,
        ret,
        avail_sz);


    FREEIF(prHqaCmd);
    return ret;
}

WLAN_STATUS wifiHqaCmdParaMonitor(char* peer_buf, unsigned short peer_len)
{
    P_HQA_CMD_FRAME prHqaCmd = NULL;
    unsigned short u2HqaCmdId = 0;
    unsigned int u4HqaCmdExtId = 0;
    unsigned int u4ParaNum = 0;
    unsigned int    value = 0;
    unsigned char* pData = NULL;

    if (peer_buf == NULL)
        return META_WIFI_STATUS_INVALID_PARA;

    if (peer_len <= 0)
        return META_WIFI_STATUS_INVALID_PARA;


    prHqaCmd = (P_HQA_CMD_FRAME)peer_buf;

    if (ntohl(prHqaCmd->magicNo) != HQA_CMD_MAGIC_NO)
    {
        DBG("MagicNo is Not Support\n");
        return META_WIFI_STATUS_NOT_SUPPORT;
    }

    u2HqaCmdId = ntohs(prHqaCmd->id);

    pData = &prHqaCmd->data[0];

    switch (u2HqaCmdId)
    {
        case HQA_CMD_OPEN_ADAPTER:
        case HQA_CMD_CLOSE_ADAPTER:
            memset(&gHqaParaInfo, 0, sizeof(gHqaParaInfo));
            break;

        case HQA_CMD_SetTxPath:
            //tx path in bitwith
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.wf_idx = value;
            //band index
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.dbdcBandIdx = value;
            break;

        case HQA_CMD_SetTxPowerExt:
            //Power
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.power = value;
            break;

        case HQA_CMD_EXTEND:
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&u4HqaCmdExtId);
            break;
    }

    switch (u4HqaCmdExtId)
    {
        case HQA_CMD_DBDCSetChannel:
            //PARA NUM
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&u4ParaNum);
            //DBDC IDX
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            //Center Channel Freq 0
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.chS1 = value;
            //Center Channel Freq 1
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.chS2 = value;
            //SystemBW
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.eCbw = value;
            //PrePketBW
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            //primary select
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            //reason
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            //channel band
            wifiHqaGetParaAndShiftBuf(TRUE, sizeof(unsigned int), &pData, (unsigned char *)&value);
            gHqaParaInfo.chBand = value;
            break;

        case HQA_CMD_DBDCStartTx:
            DBG("Start Tx: wf_idx[0x%x]dbdcIdx[%d]chBand[%d]pwr[%d]chS1[%d]chS2[%d]cbw[%d]\n",
                gHqaParaInfo.wf_idx,
                gHqaParaInfo.dbdcBandIdx,
                gHqaParaInfo.chBand,
                gHqaParaInfo.power,
                gHqaParaInfo.chS1,
                gHqaParaInfo.chS2,
                gHqaParaInfo.eCbw);
            break;

        case HQA_CMD_DBDCStopTx:
            break;

    }

    DBG("Handle cmdId = 0x%08X extId = 0x%08X\n", u2HqaCmdId, u4HqaCmdExtId);

    return META_WIFI_STATUS_SUCCESS;

}

void META_WIFI_Register(WIFI_CNF_CB callback)
{
    cnf_cb = callback;
}

void init_symbol(void)
{
    meta_data_handle = dlopen("meta_wifi_data.so", RTLD_NOW);
        if (meta_data_handle == NULL) {
            ERR("meta_wifi_data fail.\n");
        }
    
    DBG("AP_CFG_RDEB_FILE_WIFI_LID=%d", AP_CFG_RDEB_FILE_WIFI_LID);
    DBG("TEST_DATA_OK=%d", TEST_DATA_OK);
    DBG("WIFI_NVRAM_VERSION=%d", WIFI_NVRAM_VERSION);
    DBG("IS_WIFI_GEN_VER_SOC3_0=%d", IS_WIFI_GEN_VER_SOC3_0());
    DBG("IS_CFG_TSSI_CH_GT_SAME=%d", IS_CFG_TSSI_CH_GT_SAME());
    DBG("IS_CFG_DNL_CAL=%d", IS_CFG_DNL_CAL());
}


int META_WIFI_init(void)
{
    int count = 100;
    F_ID wifi_nvram_fd;
    int rec_size = 0;
    int rec_num = 0;

    if (1 == wifi_init)
    {
        ERR("wifi is already initilized.\n");
        return true;
    }

    init_symbol();

#if 0

    if (!wifi_is_loaded())
    {
        ERR("[META_WIFI] loading wifi driver ... ...\n");

        if (wifi_insmod(DRIVER_MODULE_PATH, DRIVER_MODULE_ARG) < 0)
        {
            ERR("[META_WIFI] failed to load wifi driver!!!\n");
            goto error;
        }
    }

#endif
    usleep(200000);
    /*get wifi nvram profile before wifi on */
    wifi_nvram_fd = NVM_GetFileDesc(AP_CFG_RDEB_FILE_WIFI_LID, &rec_size, &rec_num, ISWRITE);
    gNvInfo = NVM_ReadFileVerInfo(AP_CFG_RDEB_FILE_WIFI_LID);
    NVM_CloseFileDesc(wifi_nvram_fd);
    DBG("[META_WIFI] NVRAM FileVer:%s\n", gNvInfo.cFileVer);
    DBG("[META_WIFI] NVRAM FileName:%s\n", gNvInfo.cFileName);
    DBG("[META_WIFI] NVRAM RecSize:%d\n", gNvInfo.i4RecSize);
    DBG("[META_WIFI] NVRAM RecNum:%d\n", gNvInfo.i4RecNum);
    DBG("[META_WIFI] NVRAM MaxFileLid:%d\n", gNvInfo.i4MaxFileLid);

    DBG("[META_WIFI] WIFI_META_VER:%s\n", WIFI_META_VER);

#if defined(SUPPORT_AFC_C0C1)

    void *hcustlib;
    int *pui;

    hcustlib = dlopen("libcustom_nvram.so", RTLD_LAZY);
    if (!hcustlib) {
        DBG("dlopen() fail! errno=%s\n", dlerror());
        return false;
    }

    pui = (int *) dlsym(hcustlib, "iAP_CFG_CUSTOM_FILE_GPS_LID");
    if (!pui) {
        DBG("dlsym() fail! errno=%s\n", dlerror());
        dlclose(hcustlib);
        return false;
    }

    gFile_gps = *pui;

    gGpsNv = NVM_ReadFileVerInfo(gFile_gps);
    DBG("[META_WIFI] GPS NVRAM FileVer:%s\n", gGpsNv.cFileVer);
    DBG("[META_WIFI] GPS NVRAM FileName:%s\n", gGpsNv.cFileName);
    DBG("[META_WIFI] GPS NVRAM RecSize:%d\n", gGpsNv.i4RecSize);
    DBG("[META_WIFI] GPS NVRAM RecNum:%d\n", gGpsNv.i4RecNum);
    DBG("[META_WIFI] GPS NVRAM MaxFileLid:%d\n", gGpsNv.i4MaxFileLid);
#endif

    wifi_set_power(1);


    sched_yield();

    while (count-- > 0)
    {
        if (ifc_init() == 0)
        {
            if (ifc_up("wlan0") == 0)
            {
                ifc_close();
                break;
            }

            ERR("[META_WIFI] ifc_up(wlan0) failed\n");
            ifc_close();
        }
        else
        {
            ERR("[META_WIFI] ifc_init() failed\n");
        }

        usleep(100000);
    }

    if (count == 0)
        goto error;

    if (wifi_skfd == -1)
        wifi_skfd = openNetHandle();

    if (wifi_skfd < 0)
    {
        META_WIFI_deinit();
        goto error;
    }

    wifi_init = 1;
    if (META_SUPPORT_PRODUCT_LINE_CAL == 1)
        wifiProductInit();

    return true;

error:
    wifi_set_power(0);
    return false;
}

void META_WIFI_deinit(void)
{
    //int count = 20; /* wait at most 10 seconds for completion */

    DBG("[META_WIFI] WIFI_META_VER:%s\n", WIFI_META_VER);

    if (0 == wifi_init)
    {
        ERR("wifi is already deinitilized.\n");
        return;
    }

    if (wifi_skfd > 0)
    {
        closeNetHandle(wifi_skfd);
        wifi_skfd = -1;
    }

    /*    if (wifi_rmmod(DRIVER_MODULE_NAME) == 0) {
            while (count-- > 0) {
                if (!wifi_is_loaded())
                    break;
                usleep(500000);
            }
            sched_yield();*/
    wifi_set_power(0);
    /*    }*/
    wifi_init = 0;
    return;
}

void META_WIFI_OP(FT_WM_WIFI_REQ *req, char *peer_buf, unsigned short peer_len)
{
    unsigned int malloc_len;
    unsigned int i, j, pos;
    int c;
    int ret = -1;
    FT_WM_WIFI_CNF cnf;
    OID_STRUC *poid = NULL;
    unsigned int avail_sz = 0;
    NVRAM_ACCESS_STRUCT *pnvram = NULL;
    P_CMD_PL_CAL prCmdPlCal = NULL;
    char *pCmd = NULL;
    int cmdLen = 0;
    void *ret_buf = NULL, *allocated_buf = NULL;
    unsigned int ret_size = 0;
    int readByteLen = -1;

#if defined(SUPPORT_AFC_C0C1)
    // For AFC/C0C1 calibration
    WIFI_AFC_REQ *afc_req = NULL;
    WIFI_AFC_CNF afc_cnf;
    WIFI_AFC_CNF c0c1_cnf;
#endif
    /* Mulitply by 5 to align size of "0xaa " */
    char logBuf[(WFMETA_PER_LINE_DUMP_CNT * 5) + 1] = {0};
    char *pSetOidBufContent = NULL;

//modify for wifi init/deinit flow
//     if (NULL == req || NULL == peer_buf || wifi_skfd < 0 || !wifi_init) {
//         printf("[META_WIFI] Invalid arguments or operation\n");
//         goto exit;
//     }

    DBG("META_WIFI_OP OP is %d,peer_len=%d\n", req->type, peer_len);

    //for the compaliance of the former meta tool
    if (!wifi_init && WIFI_CMD_INIT != req->type)
    {
        if (true != META_WIFI_init())
        {
            ERR("!wifi_init & META_WIFI_init fail\n");
            ret = -1;
            goto exit;
        }
        else
            DBG("Init for the compaliance of the former meta tool.\n");
    }


    // OID operation
    if (WIFI_CMD_SET_OID == req->type
            || WIFI_CMD_QUERY_OID == req->type)
    {
        malloc_len = peer_len;
        if (peer_len < sizeof(OID_STRUC))
        {
            ERR("[META_WIFI] Minimum peer_len:%d\n", sizeof(OID_STRUC));
            malloc_len = sizeof(OID_STRUC);
        }

        if (NULL == (poid = (OID_STRUC *)malloc(malloc_len)))
        {
            ERR("[META_WIFI] No memory, %d\n", malloc_len);
            goto exit;
        }

        // for later freeing
        allocated_buf = (void *)poid;
        memcpy(poid, peer_buf, peer_len);

        //Dump WIFI_CMD_SET_OID and WIFI_CMD_QUERY_OID buf content
        pSetOidBufContent = (char *)poid;
        for (i = 0; i < peer_len;){
            pos = 0;
            memset(logBuf, 0, sizeof(logBuf));
            for (j = 0; j < WFMETA_PER_LINE_DUMP_CNT; j++) {
                if (pos >= sizeof(logBuf)) {
                    DBG("[META_WIFI] pos:%d out of range:%d\n", pos, sizeof(logBuf));
                    break;
                }
                if ((i + j) >= peer_len) {
                    /* will OOB access*/
                    break;
                }
                c = snprintf(logBuf + pos, sizeof(logBuf) - pos, "0x%02x ", pSetOidBufContent[i + j]);
                if (c < 0 || c >= sizeof(logBuf) - pos) {
                    DBG("[META_WIFI] snprintf fail c:%d, pos:d\n", c, pos);
                    break;
                }
                pos += c;
            }
            DBG ("[META_WIFI] OIDReq [%04x] :%s", i, logBuf);
            i += WFMETA_PER_LINE_DUMP_CNT;
        }

        if (WIFI_CMD_SET_OID == req->type)
        {
            ret = setIWreq(wifi_skfd, "wlan0", poid->SetOidPara.oid,
                           poid->SetOidPara.data, poid->SetOidPara.dataLen, &avail_sz);
            DBG("[META_WIFI] SET_OID, OID: 0x%x, len: %d, ret: %d\n",
                poid->SetOidPara.oid, poid->SetOidPara.dataLen, ret);
        }
        else if (WIFI_CMD_QUERY_OID == req->type)
        {
            ret = getIWreq(wifi_skfd, "wlan0", poid->QueryOidPara.oid,
                           poid->QueryOidPara.data, poid->QueryOidPara.dataLen, &avail_sz);
            DBG("[META_WIFI] QUERY_OID, OID: 0x%x, len: %d, ret: %d\n",
                poid->QueryOidPara.oid, poid->QueryOidPara.dataLen, ret);
        }

        if (ret == 0 && WIFI_CMD_QUERY_OID == req->type)
        {
            ret_buf = (void *)poid;
            ret_size = avail_sz + 8;
        }
    }
    // NVRAM access
    else if (WIFI_CMD_NVRAM_WRITE_ACCESS == req->type
             || WIFI_CMD_NVRAM_READ_ACCESS == req->type)
    {
        malloc_len = peer_len;
        if (peer_len < sizeof(NVRAM_ACCESS_STRUCT))
        {
            ERR("[META_WIFI] Minimum len:%d\n", sizeof(NVRAM_ACCESS_STRUCT));
            malloc_len = sizeof(NVRAM_ACCESS_STRUCT);
        }

        if (NULL == (pnvram = (NVRAM_ACCESS_STRUCT *)malloc(malloc_len)))
        {
            ERR("[META_WIFI] No memory, %d\n", malloc_len);
            goto exit;
        }

        // for later freeing
        allocated_buf = (void *)pnvram;
        memcpy(pnvram, peer_buf, peer_len);

        if (peer_len < (offsetof(NVRAM_ACCESS_STRUCT, data) + pnvram->dataLen))
        {
            ERR("[META_WIFI] Mimatched NVRAM content length: (%d / %u)\n", peer_len,
                (unsigned int)(offsetof(NVRAM_ACCESS_STRUCT, data) + pnvram->dataLen));
            goto exit;
        }

        if (WIFI_CMD_NVRAM_READ_ACCESS == req->type)
        {
            readByteLen = wifiNVRAMCtrl(NVRAM_READ, pnvram);

            if (readByteLen > 0)
            {
                ret_buf = (void *)pnvram;
                ret_size = offsetof(NVRAM_ACCESS_STRUCT, data) + readByteLen;
                ret = 0;
            }
            else
                ret = -1;
        }
        else if (WIFI_CMD_NVRAM_WRITE_ACCESS == req->type)
        {
            ret = wifiNVRAMCtrl(NVRAM_WRITE, pnvram);
        }
    }
    else if (WIFI_CMD_INIT == req->type)
    {
        if (true != META_WIFI_init())
            ret = -1;
        else
            ret = 0;
    }

    else if (WIFI_CMD_DEINIT == req->type)
    {
        META_WIFI_deinit();
        ret = 0;
    }

    else if (WIFI_CMD_SCRIPT == req->type)
    {
        /*Do sanity check*/
        if (peer_len <= 0)
            goto exit;

        /*memory allocate for saving driver's command result*/
        if (NULL == (pCmd = (char *)malloc(WIFI_SCRIPT_TOTAL_BUF_LEN)))
        {
            goto exit;
        }

        memcpy(pCmd, peer_buf, peer_len);
        pCmd[peer_len] = '\0';

        /*parse User command and remove iwpriv driver command head, for example : adb shell ipwriv driver*/
        cmdLen = wifiScriptRemoveHead(pCmd);

        if (cmdLen > 0)
        {
            /*Support Product Line Calibration script*/
            /*format : PL <ID> <ACT> <VALUE>*/
            if (pCmd[0] == 'P' && pCmd[1] == 'L')
            {
                if (META_SUPPORT_PRODUCT_LINE_CAL == 1)
                    ret = wifiProductLineScript(pCmd, cmdLen);
            }
            else
            {
                ret = driverIWreq(wifi_skfd, "wlan0", pCmd, cmdLen, &avail_sz);
                DBG("[META_WIFI] DRIVER CMD:%s,len:%d,ret:%d,avail_sz:%d\n", peer_buf, peer_len, ret, avail_sz);
            }

            if (ret == 0 && WIFI_CMD_SCRIPT == req->type)
            {
                ret_buf = (void *)pCmd;
                ret_size = avail_sz;
            }

        }

    }

    else if (WIFI_CMD_HQA == req->type)
    {

        if ((peer_len <= 0) || NULL == (pCmd = (char *)malloc(WIFI_SCRIPT_TOTAL_BUF_LEN)))
        {
            ERR("[HQA_CMD] No memory, %d\n", peer_len);
            goto exit;
        }

        if (peer_len > WIFI_SCRIPT_TOTAL_BUF_LEN){ // avoid OOB write
            ERR("[HQA_CMD] Invalid peer_len :%d\n", peer_len);
            goto exit;
        }

        memcpy(pCmd, peer_buf, peer_len);
        pCmd[peer_len] = '\0';
        cmdLen = peer_len; // +  RA_CFG_HLEN

        ret = HQAIWreq(wifi_skfd, "wlan0", pCmd, cmdLen, &avail_sz);

        DBG("[HQA_CMD] CMD:%p,len:%d,ret:%d,avail_sz:%d\n", peer_buf, peer_len, ret, avail_sz);

        if (ret == 0 && WIFI_CMD_HQA == req->type)
        {
            if (META_SUPPORT_PRODUCT_LINE_CAL == 1)
                wifiHqaCmdParaMonitor(peer_buf, peer_len);
            ret_buf = (void *)pCmd;
            ret_size = avail_sz;
        }
    }
    else if (WIFI_CMD_PL_CALIBRATION == req->type && META_SUPPORT_PRODUCT_LINE_CAL == 1)
    {
        if ((peer_len <= 0) || NULL == (prCmdPlCal = (P_CMD_PL_CAL)malloc(sizeof(CMD_PL_CAL))))
        {
            ERR("[META_WIFI] No memory, %d\n", peer_len);
            goto exit;
        }

        //init
        memset(prCmdPlCal, 0, sizeof(CMD_PL_CAL));

        // for later freeing
        memcpy(prCmdPlCal, peer_buf, peer_len);
        ret = wifiProductLineCalProcess(prCmdPlCal);

        ret_buf = (void *)prCmdPlCal;
        ret_size = sizeof(CMD_PL_CAL);

        DBG("[PL_CAL] ID:%d,ACT:%d,ret:%d,ret_size:%d done!\n", prCmdPlCal->calId, prCmdPlCal->action, ret, ret_size);
    }


#if defined(SUPPORT_AFC_C0C1)
    else if (WIFI_CMD_AFC_CALIBRATION == req->type)
    {
        int cap_id = 0;
        float afc_temperature = 0;
        int nvram_ret = 0;

		DBG("[META_CAL] WIFI_CMD_AFC_CALIBRATION\n");

        if ((peer_len <= 0) || NULL == (afc_req = (WIFI_AFC_REQ*)malloc(sizeof(WIFI_AFC_REQ))))
        {
            ERR("[META_CAL] Fail to allocate WIFI_AFC_REQ, %d\n", peer_len);
            goto exit;
        }

        if (peer_len > sizeof(WIFI_AFC_REQ)){ // avoid OOB write
            ERR("[META_CAL] Invalid peer_len :%d\n", peer_len);
            goto exit;
        }

        // Get operation in peer buffer
        memset(afc_req, 0, sizeof(WIFI_AFC_REQ));
        memcpy(afc_req, peer_buf, peer_len);
        ret = 0;

        if (afc_req->cmd_id == WIFI_AFC_Calibration) {
            if (afc_req->cmd.afc_cmd.Action == 1) {
                // Action 0 for start; 1 for stop
                DBG("[META_CAL] Stop AFC calibration\n");
                goto exit;
            }

            memset(&afc_cnf, 0, sizeof(WIFI_AFC_CNF));
            afc_cnf.cmd_id = WIFI_AFC_Calibration;
            afc_cnf.cmd.afc_cmd.Status = META_SUCCESS;

            DBG("[META_CAL] Start AFC calibration\n");
            cap_id = start_afc_calibration(&afc_temperature);

            if (cap_id >= 0) {
                nvram_ret = CO_TMS_NVRAMCtrl(&gNvInfo, AP_CFG_RDEB_FILE_WIFI_LID, 0xF8, (unsigned char)cap_id);
                DBG("[META_CAL] Write cap_id[%d]=[0x%x] into wifi nvram\n", cap_id, cap_id);

                if (nvram_ret == META_WIFI_STATUS_FAIL) {
                    afc_cnf.cmd.afc_cmd.Status = META_FAILED;
                    ret = -1;
                    ERR("[META_CAL] Fail to write cap id %d into wifi nvram\n", cap_id);
                }
            } else {
                afc_cnf.cmd.afc_cmd.Status = META_FAILED;
                ret = -1;
                ERR("[META_CAL] Fail to get valid cap id, cal_ret[%d]\n", cap_id);
            }

            afc_cnf.cmd.afc_cmd.CAP_ID = cap_id;
            afc_cnf.cmd.afc_cmd.Temperature = afc_temperature;
            ret_buf = (void *)&afc_cnf;
            ret_size = sizeof(WIFI_AFC_CNF);
            DBG("[META_CAL] Send AFC CNF: Status[%d], CAP_ID[%d], Temperature[%f]", afc_cnf.cmd.afc_cmd.Status, afc_cnf.cmd.afc_cmd.CAP_ID, afc_cnf.cmd.afc_cmd.Temperature);

        } else if (afc_req->cmd_id == WIFI_C0C1_Calibration) {
            if (afc_req->cmd.c0c1_cmd.Action == 1) {
                // Action 0 for start; 1 for stop
                DBG("[META_CAL] Stop C0C1 calibration\n");
                goto exit;
            }

            unsigned int ui_c0 = 0;
            unsigned int ui_c1 = 0;
            unsigned char token = 0;
            int cal_ret = 0;
            float c0c1[2] = {0};
            unsigned int nvram_c0[4] = {0x24, 0x25, 0x26, 0x27};
            unsigned int nvram_c1[4] = {0x28, 0x29, 0x2A, 0x2B};
            C0C1_INIT init;
            init.c1 = afc_req->cmd.c0c1_cmd.C1;
            init.centerFreq = afc_req->cmd.c0c1_cmd.CenterFreq;
            init.temperature = afc_req->cmd.c0c1_cmd.Temperature;

            memset(&c0c1_cnf, 0, sizeof(WIFI_AFC_CNF));
            memset(gTemperature, 0, 12 * sizeof(float));
            memset(gFoeppm, 0, 12 * sizeof(float));

            c0c1_cnf.cmd_id = WIFI_C0C1_Calibration;
            c0c1_cnf.cmd.c0c1_cmd.Status = META_SUCCESS;

            DBG("[META_CAL] Start C0C1 calibration, init c1[%f], centerFreq[%f], temperature[%f]\n", init.c1, init.centerFreq, init.temperature);
            cal_ret = start_c0c1_calibration(&init, c0c1);

            if (cal_ret == 0) {
                ui_c0 = *(unsigned int *)&(c0c1[0]);
                ui_c1 = *(unsigned int *)&(c0c1[1]);
                DBG("[META_CAL] Convert float c0[%f], c1[%f] to unsigned int c0[0x%x], c1[0x%x]", c0c1[0], c0c1[1], ui_c0, ui_c1);

                // Since nvram only read/write unsigned char, we need to
                // break (4 bytes: unsigned int) into 4 (1 byte: unsigned char)
                for (int i = 0; i < 4; i++) {
                    token = (ui_c0 >> (i * 8)) & 0xFF;
                    DBG("[META_CAL] Write c0 token_%d[0x%x] into gps nvram addr[0x%x]", i, token, nvram_c0[i]);

                    nvram_ret = CO_TMS_NVRAMCtrl(&gGpsNv, gFile_gps, nvram_c0[i], token);

                    if (nvram_ret == META_WIFI_STATUS_FAIL) {
                        c0c1_cnf.cmd.c0c1_cmd.Status = META_FAILED;
                        ret = -1;
                        ERR("[META_CAL] Fail to write valid c0[%f] into gps nvram, token[%d]\n", c0c1[0], i);
                        break;
                    }
                }

                for (int i = 0; i < 4; i++) {
                    token = (ui_c1 >> (i * 8)) & 0xFF;
                    DBG("[META_CAL] Write c1 token_%d[0x%x] into gps nvram addr[0x%x]", i, token, nvram_c1[i]);

                    nvram_ret = CO_TMS_NVRAMCtrl(&gGpsNv, gFile_gps, nvram_c1[i], token);

                    if (nvram_ret == META_WIFI_STATUS_FAIL) {
                        c0c1_cnf.cmd.c0c1_cmd.Status = META_FAILED;
                        ret = -1;
                        ERR("[META_CAL] Fail to write valid c1[%f] into gps nvram, token[%d]\n", c0c1[1], i);
                        break;
                    }
                }
            } else {
                c0c1_cnf.cmd.c0c1_cmd.Status = META_FAILED;
                ret = -1;
                ERR("[META_CAL] Fail to get valid c0c1, cal_ret[%d]\n", cal_ret);
            }

            c0c1_cnf.cmd.c0c1_cmd.C0 = c0c1[0];
            c0c1_cnf.cmd.c0c1_cmd.C1 = c0c1[1];
            memcpy(c0c1_cnf.cmd.c0c1_cmd.tmp, gTemperature, 12 * sizeof(float));
            memcpy(c0c1_cnf.cmd.c0c1_cmd.foeppm, gFoeppm, 12 * sizeof(float));
            ret_buf = (void *)&c0c1_cnf;
            ret_size = sizeof(WIFI_AFC_CNF);

            DBG("[META_CAL] Send C0C1 CNF: Status[%d], C0[%f], C1[%f]", c0c1_cnf.cmd.c0c1_cmd.Status, c0c1_cnf.cmd.c0c1_cmd.C0, c0c1_cnf.cmd.c0c1_cmd.C1);
        }
    }
#endif

exit:
    memset(&cnf, 0, sizeof(FT_WM_WIFI_CNF));
    cnf.header.token = req->header.token;
    cnf.header.id    = FT_WIFI_CNF_ID;
    cnf.type         = req->type;
    cnf.status       = META_SUCCESS;

    /* CHECKME!! Need to confirm the value of drv_status */
    cnf.drv_status   = (ret == 0) ? (int)true : (int)false;

    wifi_send_resp(&cnf, ret_buf, ret_size);

    FREEIF(poid);
    FREEIF(pnvram);
    FREEIF(pCmd);
    FREEIF(prCmdPlCal);
#if defined(SUPPORT_AFC_C0C1)
    FREEIF(afc_req);
#endif
    return;
}

